export default {
  root: {
    title: "LLM-Kit"
  },
  nav: {
    home: "首页",
    data: "数据管理",
    construct: "数据集构建",
    quality: "质量评估",
    dedup: "数据去重"
  },
  home: {
    hero: {
      title: "LLM-Kit: 大语言模型知识迭代工具包",
      subtitle: "先进的数据迭代优化工具，用于知识精炼和大语言模型开发",
      get_started: "开始使用"
    },
    features: {
      heading: "核心功能",
      data: {
        title: "数据管理",
        description: "创建和管理数据池，上传数据集，执行操作"
      },
      construct: {
        title: "数据集构建",
        description: "通过各种方法构建高质量的训练数据集"
      },
      quality: {
        title: "质量评估",
        description: "评估数据集质量以确保训练效果"
      },
      dedup: {
        title: "数据去重",
        description: "识别和删除重复数据以提高训练效率"
      },
      deploy: {
        title: "部署管理",
        description: "简化模型部署流程并优化推理性能"
      },
      config: {
        title: "系统设置",
        description: "配置系统参数和管理模型列表"
      }
    },
    about: {
      title: "关于 LLM-Kit",
      description: "LLM-Kit 是一个知识迭代工具包，旨在优化大语言模型数据处理。它帮助您通过迭代循环精炼和改进数据集，在从数据收集到评估和部署的每个步骤中提升模型性能。"
    }
  },
  sidebar: {
    data_manager: "数据管理",
    dataset_construct: "数据集构建",
    quality_eval: "质量评估",
    deduplication: "数据去重",
    deployment_manager: "部署管理",
    record: "记录管理",
    settings: "设置",
  },
  components: {
    model_card: {
      adapters: "适配器",
      id: "ID",
      name_and_description: "名称和描述",
      action: "操作",
    },
    go_back: "返回",
    visibility_button: {
      hide: "私有",
      publicize: "公开",
    },
    data: {
      data_pool_selector: "数据池:",
      data_pool_des: "选择一个数据池",
      data_set_selector: "数据集:"
    },
    eval_metrics_description: {
      acc_des: "正确预测样本数/总样本数",
      recall_des: "基于召回率判断两个句子的相似性",
      f1score_des: "准确率和召回率的调和平均",
      pre_des: "评估生成文本中与参考文本匹配的内容比例",
      bleu_des: "基于准确率判断两个句子的相似性",
      distinct_des: "反映文本生成的多样性"
    },
    device: {
      GPU_utilization: "GPU 使用率:",
      memory_utilization: "GPU 内存:",
    },
    deployment_params: {
      title: "部署参数",
      subtitle: "量化参数",
      subsubtitle: "量化部署参数",
      bits_and_bytes: "是否使用 bits_and_bytes",
      use_flash_attention: "是否使用 flash attention",
      use_deepspeed: "是否使用 deepspeed",
      use_vllm: "是否使用 vllm",
      description: "部署参数"
    }
  },
  data: {
    title: "数据管理",
    description: "管理和组织您的数据",
    upload: {
      title: "数据上传",
    },
    preview: "预览",
    download: "下载",
    create_pool: "创建数据池",
    no_dataset: "数据池中没有数据集",
    detail: {
      title: "详情",
      detail: "数据池详情",
      delete: "删除此数据池",
      filter: "筛选",
      create_on: "创建时间:",
      size: "数据量:",
      title_1: "确认删除?",
      p1: "您确定要删除吗?",
      p2: "数据将",
      p3: "无法",
      p4: "恢复。",
      yes: "是",
      no: "否",
      title_2: "暂存区中还有未提交的数据",
      p5: "您确定要返回吗？暂存区中还有未提交的数据。",
      p6: "暂存区中的数据将",
      p7: "不会",
      p8: "被保存。",
    },
    table: {
      col_name: "名称",
      col_time: "时间",
      col_size: "大小",
      col_format: "格式",
      col_des: "描述",
      col_operation: "操作"
    },
    delete: {
      title: "确认删除",
      data: "删除数据",
      p1: "您确定要删除此数据集吗?",
      p2: "删除后数据将无法恢复。",
      yes: "删除",
      no: "否"
    },
    construct: {
      title: "标题",
      uploaded_files:"已解析文件列表",
      type:"类型",
      size:"大小",
      deselect_all:'取消全选',
      select_all:'全选',
      erine:'文心',
      flash:'闪电',
      qwen:'通义千问',
      content:'内容',
      reasoning_process:'推理过程',
      lite:'轻量版',
      cot_generating:'CoT生成中',
      qa_delete_success:"问答删除成功",
      latex_converting:'LaTeX转换中',
      qa_generating:"问答生成中",
      qa_generating_wait:'问答生成等待中',
      raw_file_preview_for_file:'文件名:',
      upload_status:'状态(点击预览)',
      filename:'文件名(点击预览)',
      qa_generation_settings:"参数输入",
      main_settings:"问答对生成",
      cotgenerate:'CoT问答对生成',
      raw_file_preview_title:'原始文件预览',
      delete_qa_button:'删除问答',
      status_generated:"已构建,",
      status_generating:'未构建',
      delete_button:'删除',
      delete_confirmation_title:"确认删除",
      delete_confirmation_message:'是否删除',
      delete_confirm_button:'确认',
      delete_cancel_button:'取消',
      modification_time:'创建时间',
      parallel_num:'并行数',
      sk:"密钥",
      ak:'API密钥',
      qa_preview_title: "问答预览",
      cot_preview_title:'CoT预览',
      no_reasoning_available:'无推理结果',
      qa_preview_for_file: "原始文件名:",
      question: "问题",
      answer: "答案",
      text_context: "上下文文本",
      previous_page: "上一页",
      next_page: "下一页",
      page: "页",
      go_to_page: "跳转到页:",
      preview_qa_fetch_failed: "获取问答内容预览失败",
      preview_qa_network_error: "获取问答内容网络错误",
      no_qa_content_available: "此文件没有可用的问答内容。",
      preview_cot_fetch_failed: "获取CoT内容预览失败",
      preview_cot_network_error: "获取CoT内容网络错误",
      no_cot_content_available: "此文件没有可用的CoT内容。",
      save_path:'保存名称',
      save_path_placeholder:"默认原文件名",
      domain:'数据集领域信息',
      generate_button:'生成',
      p1: "原始数据集",
      AK: "API密钥：",
      AU: "API地址：",
      prompt: "提示词：",
      name: "名称：",
      des: "描述：",
      begin: "开始",
      zone: "暂存区",
      model_select:"模型选择",
      no_file: "无文件",
      submit: "提交",
      model_name: "模型名称：",
      progress: "进度：",
      remain_time: "剩余时间：",
      constructing: "构建中",
      construct_finish: "构建完成！",
      wait: "等待",
      invalid_sk_ak:"无效的密钥和API密钥",
      subtitle: "副标题",
      create_task: "创建任务",
      next_step: "下一步",
      previous_step: "上一步",
    },
    uploader: {
      fetch_fail:'文件获取失败',
      title:'文件管理',
      col_filename: "文件名",
      upload_status:'状态' ,
      parsed:'已解析',
      pending:'未解析',
      parse_button:"解析",
      action:'操作',
      delete_action:'删除操作',
      delete_button: '删除',
      col_datasetname: "数据集名称",
      col_des: "描述",
      filename:"文件名",
      file_type:"文件类型",
      parse_status:"解析状态",
      size:"文件大小",
      created_at:"上传时间",
      failed:"结束",
      uploaded_files:"已上传文件",
      col_option: "操作",
      datapool_detail: "数据池详情",
      zone: "暂存区",
      format: "格式",
      submit: "提交暂存区中的所有文件",
      no_file: "暂存区中没有上传的文件",
      enter_name: "输入数据集名称",
      enter_des: "输入数据集描述",
      move: "移出暂存区",
      click: "点击 ",
      or: "或",
      p1: " 拖拽 ",
      p2: "上传文件到暂存区"
    },
    task: {
      steps: {
        infor: "基本信息",
        upload: "上传数据",
        infor_des: "填写创建数据池的基本信息",
        upload_des: "选择要上传的数据"
      },
      p1: "您确定已完成创建吗？暂存区中还有未提交的数据。",
      p2: "暂存区中的数据将",
      p3: "不会",
      p4: "被保存。",
      yes: "是",
      no: "否",
      title: "创建数据池",
      description: "按照提示步骤创建数据池",
      complete: "完成",
      name: "数据池名称",
      enter_name: "请输入数据池名称",
      des: "数据池描述",
      enter_des: "请输入数据池描述"
    },
    filter: {
      title: "数据筛选",
      p1: "原始数据集:",
      p2: "保留比例:",
      name: "新数据集名称:",
      des: "新数据集描述:",
      begin: "开始筛选"
    }
  },
  fault: {
    title: "错误日志",
    description: "查看任务和进程中的错误日志",
    message: "消息",
    source: "来源",
    time: "时间",
    code: "代码",
    action: "操作",
    view_logs: "查看日志",
    download_logs: "下载日志",
    search_placeholder: "输入用逗号分隔的标签",
    search: "搜索",
    management: "微调管理",
    finetune: "微调",
    create_task: "创建微调任务",
    wordcloud: "词云",
    close: "关闭"
  },
  config: {
    log_out: "退出登录",
    model_list: "模型列表",
    title: "设置",
    description: "配置系统参数"
  },
  deduplication: {
    title: "数据去重",
    select:  "选择",
    description: "对数据集进行去重以确保数据集质量",
    min_answer_length: "最小答案长度:",
    dedup_by_answer: "按答案去重:",
    dedup_threshold: "去重阈值:",
    action: "操作",
    close: "关闭",
    params: "参数",
    begin: "开始去重",
    result_title: "去重结果",
    no_deleted_data: "没有删除的数据",
    return: "返回"
  },
  quality_eval: {
    title: "质量控制",
    subtitle: "提高数据集的质量",
    start: "开始质量控制",
    model_name: "模型选择:",
    name: "输入文件名:",
    domain: "新数据集描述:",
    optional: "(可选)",
    max_attempts: "最大尝试次数:",
    similarity_rate: "相似度:",
    coverage_rate: "覆盖率:",
    params: "参数",
    parallel_num: "并行数:",
    AK: "API密钥",
    SK: "密钥",
    qa_files: "问答对文件",
    files: {
      record_id: "ID",
      filename: "文件名",
      create_at: "创建时间",
      select: "选择"
    },
    progress: {
      progress: "进度",
      processing: "处理中",
      completed: "完成",
      failed: "失败",
      timeout: "超时",
      error: "错误",
      not_found: "未找到",
      remain_time: "剩余时间:",
      error_info: "错误信息:",
      wait: "等待",
    }
  },
  construct: {
    title: "数据集构建",
    subtitle: "构建问答对数据集",
    create_task: "创建构建任务",
    next_step: "下一步",
    previous_step: "上一步",
    main_settings: "构建设置",
    uploaded_files: "已解析文件",
    filename: "文件名",
    upload_status: "状态",
    select_all: "全选",
    deselect_all: "取消全选",
    status_generated: "已生成",
    status_generating: "生成中...",
    status_unknown: "未生成",
    delete_qa_button: "删除问答",
    delete_cot_button: "删除CoT",
    delete_button: "删除选中",
    latex_converting: "LaTeX转换中...",
    qa_generating: "问答对生成中...",
    qa_generated_success: "问答生成完成",
    qa_generation_failed: "问答生成失败",
    qa_generation_network_error: "问答生成网络错误",
    latex_conversion_failed: "LaTeX转换失败",
    latex_conversion_network_error: "LaTeX转换网络错误",
    qa_generation_settings: "问答生成设置",
    cot_generation_settings: "CoT生成设置",
    parallel_num: "并行处理线程",
    save_path: "保存路径",
    save_path_placeholder: "保存生成文件的路径",
    model_name: "模型",
    erine: "文心",
    flash: "闪电",
    lite: "轻量版",
    qwen: "通义千问",
    sk: "密钥",
    ak: "访问密钥",
    domain: "领域",
    domain_placeholder: "输入领域(可选)",
    generate_qa_button: "生成问答对",
    generating_qa: "问答对生成中...",
    qa_delete_success: "问答文件删除成功",
    qa_delete_failed: "问答文件删除失败",
    qa_delete_network_error: "删除问答文件网络错误",
    qa_preview_failed: "问答文件预览失败",
    qa_preview_network_error: "预览问答文件网络错误",
    generate_cot_button: "生成CoT数据",
    generating_cot: "CoT数据生成中...",
    cot_delete_success: "CoT文件删除成功",
    cot_delete_failed: "CoT文件删除失败",
    cot_delete_network_error: "删除CoT文件网络错误",
    cot_preview_failed: "CoT文件预览失败",
    cot_preview_network_error: "预览CoT文件网络错误",
    delete_confirmation_title: "确认删除",
    delete_confirmation_message: "您确定要删除选中的文件吗？此操作无法撤销。",
    delete_confirm_button: "删除",
    delete_cancel_button: "取消",
    qa_preview_title: "问答预览",
    cot_preview_title: "CoT预览",
    no_reasoning_available: "无可用结果",
    qa_preview_for_file: "原始文件名:",
    question: "问题",
    answer: "答案",
    text_context: "上下文文本",
    previous_page: "上一页",
    next_page: "下一页",
    page: "页",
    go_to_page: "跳转到页:",
    preview_qa_fetch_failed: "获取问答内容预览失败",
    preview_qa_network_error: "获取问答内容网络错误",
    no_qa_content_available: "此文件没有可用的问答内容。",
    preview_cot_fetch_failed: "获取CoT内容预览失败",
    preview_cot_network_error: "获取CoT内容网络错误",
    no_cot_content_available: "此文件没有可用的CoT内容。",
    invalid_sk_ak: "无效的密钥和访问密钥",
    qa_preview: "问答预览",
    cot_preview: "CoT预览",
    file_preview: "文件:",
    raw_file_preview_title: "文件预览",
    content: "内容",
    reasoning: "推理",
    context: "上下文",
    previous: "上一页",
    next: "下一页",
    go_to: "跳转到页:",
    no_content: "无可用内容",
    no_reasoning: "无可用推理",
    preview_failed: "获取内容失败",
    network_error: "获取内容网络错误",
    no_file_selected: "未选择文件",
    preview_raw_fetch_failed: "获取文件内容失败",
    preview_raw_network_error: "获取文件内容网络错误",
    raw_file_preview_for_file: "文件:",
    no_raw_content_available: "此文件没有可用内容",
    cot_generating: "CoT数据生成中...",
    cot_generated_success: "CoT生成完成",
    cot_generation_failed: "CoT生成失败",
    cot_generation_network_error: "CoT生成网络错误",
    cot_all_generated_success: "所有CoT生成完成",
    action: "操作",
    go: "跳转"
  },
  record: {
    title: "记录管理",
    description: "管理所有任务记录。",
    search: "搜索",
    view_logs: "查看日志",
    message: "消息",
    time: "时间",
    source: "来源",
    action: "操作",
    tasks: {
      parse: "解析",
      to_tex: "转换为TeX",
      generate_qa: "生成问答"
    }
  },
  search: "搜索",
  view_logs: "查看日志",
  delete: "删除",
  // 添加缺失的翻译
  "Success": "成功",
  "Total Files": "文件总数",
  "QA Generated": "已生成问答",
  "CoT Generated": "已生成CoT",
  "files": "文件",
  "selected": "已选择",
  "重试": "重试",
  "文件类型": "文件类型",
  "文件大小": "文件大小",
  "创建时间": "创建时间",
  "LaTeX conversion": "LaTeX转换",
  "Completed": "已完成",
  "Processing": "处理中",
  "Processed chunks": "已处理块",
  "Elapsed time": "已用时间",
  "Remaining time": "剩余时间",
  "Refreshing progress every second...": "每秒刷新进度...",
  "Abort task": "中止任务",
  "QA generation": "问答生成",
  "And": "还有",
  "more file(s)...": "个文件...",
  // 数据管理页面翻译
  "Parsed Files": "已解析文件",
  "Pending Files": "待处理文件",
  "Upload New Files": "上传新文件",
  "Supported formats": "支持的格式",
  "Preview": "预览",
  "Confirm the deletion": "确认删除",
  "Are you sure you want to delete this file?": "您确定要删除此文件吗？",
  "Type": "类型",
  "Size": "大小",
  "Cancel": "取消",
  "Confirm": "确认",
  "Loading...": "加载中...",
  "No content available": "无可用内容",
  "Download": "下载",
  "Close": "关闭",
  // DatasetTable 组件翻译
  "Generated QA Datasets": "已生成问答数据集",
  "datasets available": "个数据集可用",
  "Name": "名称",
  "Operations": "操作",
  "Delete Dataset": "删除数据集",
  "Are you sure you want to delete this dataset?": "您确定要删除此数据集吗？",
  "This action cannot be undone!": "此操作无法撤销！",
  "Yes, Delete": "是，删除",
  "Question": "问题",
  "Answer": "答案",
  "Loading preview...": "加载预览中...",
  "Previous": "上一页",
  "Next": "下一页",
  "Page": "第",
  "of": "页，共",
  "No content available": "无可用内容",
  "No datasets available": "无可用数据集"
};

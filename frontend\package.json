{"name": "frontend", "version": "0.0.1", "scripts": {"dev": "vite dev --host 0.0.0.0", "build": "vite build", "preview": "vite preview", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch"}, "devDependencies": {"@fontsource/fira-mono": "^4.5.10", "@neoconfetti/svelte": "^1.0.0", "@sveltejs/adapter-auto": "^3.3.1", "@sveltejs/kit": "^2.8.0", "@sveltejs/vite-plugin-svelte": "^5.0.1", "autoprefixer": "^10.4.19", "postcss": "^8.4.38", "prettier": "^3.2.5", "prettier-plugin-svelte": "^3.2.3", "svelte": "^5.1.15", "svelte-check": "^3.6.0", "tailwindcss": "^3.4.3", "tslib": "^2.4.1", "typescript": "^5.0.0", "vite": "^6.0.11"}, "type": "module", "dependencies": {"@sveltejs/adapter-node": "^5.0.1", "@sveltejs/adapter-static": "^3.0.1", "@types/axios": "^0.14.0", "adapter": "^1.0.0-beta.10", "apexcharts": "^3.48.0", "axios": "^1.6.8", "date-fns": "^4.1.0", "flowbite": "^2.3.0", "flowbite-svelte": "^0.47.4", "flowbite-svelte-icons": "^1.5.0", "from": "^0.1.7", "i18next": "^23.10.1", "import": "^0.0.6", "svelte-i18next": "^2.2.2", "tailwind-merge": "^2.2.2", "terser": "^5.30.3", "vite-plugin-compression": "^0.5.1"}}
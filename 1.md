# LLM-Kit项目进展报告

## 1. 已完成的软件系统功能需求设计

### 用户故事

#### 文件管理模块
1. **文件上传**
   - 作为用户，我希望能够上传文本文件（tex、txt、json），以便进行后续处理
   - 作为用户，我希望能够上传二进制文件（pdf、jpg、jpeg、png），以便进行解析和内容提取

2. **文件列表查看**
   - 作为用户，我希望查看所有已上传的文件列表，包括文件名、类型、大小和上传时间
   - 作为用户，我希望能够区分文本文件和二进制文件

3. **文件内容预览**
   - 作为用户，我希望能够预览已上传文件的原始内容
   - 作为用户，我希望能够预览解析后的文件内容

4. **文件删除**
   - 作为用户，我希望能够删除不再需要的文件，以节省存储空间

#### 文本解析模块
1. **文件解析**
   - 作为用户，我希望系统能够解析上传的文本文件，提取有用内容
   - 作为用户，我希望系统能解析特定文件ID对应的文件内容

2. **解析进度跟踪**
   - 作为用户，我希望看到文件解析的实时进度
   - 作为用户，我希望能够查询特定文件的解析状态和进度

#### LaTeX转换模块
1. **LaTeX转换**
   - 作为用户，我希望将解析后的文本转换为LaTeX格式，便于学术内容处理
   - 作为用户，我希望能够使用多个API密钥并行处理，加快转换速度

2. **转换进度跟踪**
   - 作为用户，我希望实时查看LaTeX转换的进度百分比
   - 作为用户，我希望看到预估的完成时间和已用时间
   - 作为用户，我希望能够中止正在进行的转换任务

3. **转换结果查看**
   - 作为用户，我希望查看所有已转换文件的列表
   - 作为用户，我希望查看特定文件的LaTeX转换结果

#### 问答对生成模块
1. **问答对生成**
   - 作为用户，我希望基于LaTeX内容生成问答对
   - 作为用户，我希望指定使用的模型和领域
   - 作为用户，我希望能够使用多个API密钥并行生成，提高效率

2. **生成进度跟踪**
   - 作为用户，我希望实时查看问答对生成的进度
   - 作为用户，我希望了解预估完成时间和已处理的块数
   - 作为用户，我希望能够中止耗时过长的生成任务

3. **问答对管理**
   - 作为用户，我希望查看生成的问答对列表
   - 作为用户，我希望预览和下载生成的问答对
   - 作为用户，我希望删除不需要的问答对数据集

## 2. 已完成数据库设计

### 数据模型

#### UploadedFile (uploaded_files集合)
```
{
    _id: ObjectId,                                // 文件ID
    filename: String,                            // 文件名
    content: String,                             // 文件内容
    file_type: String,                           // 文件类型(tex/txt/json)
    size: Integer,                               // 文件大小(字节)
    status: String,                              // 文件状态(pending/processing/completed/failed)
    created_at: DateTime                         // 创建时间
}
```

#### UploadedBinaryFile (uploaded_binary_files集合)
```
{
    _id: ObjectId,                               // 文件ID
    filename: String,                            // 文件名
    content: Binary,                             // 二进制内容
    file_type: String,                           // 文件类型(pdf/jpg/jpeg/png)
    mime_type: String,                           // MIME类型
    size: Integer,                               // 文件大小(字节)
    status: String,                              // 文件状态(to_parse/processing/completed/failed)
    created_at: DateTime                         // 创建时间
}
```

#### ParseRecord (parse_records集合)
```
{
    _id: ObjectId,                               // 记录ID
    input_file: String,                          // 输入文件名
    status: String,                              // 处理状态(processing/completed/failed)
    file_type: String,                           // 文件类型
    save_path: String,                           // 保存路径
    task_type: String,                           // 任务类型(parse)
    progress: Integer,                           // 进度百分比(0-100)
    content: String,                             // 解析后内容
    parsed_file_path: String,                    // 解析后文件路径
    created_at: DateTime,                        // 创建时间
    error_message: String                        // 错误信息(如果有)
}
```

#### TexConversionRecord (tex_records集合)
```
{
    _id: ObjectId,                               // 记录ID
    input_file: String,                          // 输入文件名
    original_file: String,                       // 原始文件名
    status: String,                              // 状态(processing/completed/failed/timeout/aborted)
    model_name: String,                          // 使用的模型名称
    progress: Integer,                           // 进度百分比(0-100)
    content: Array,                              // 转换结果数组
    start_time: DateTime,                        // 开始时间
    created_at: DateTime,                        // 创建时间
    error_message: String,                       // 错误信息
    chunk_info: {                                // 分块信息
        total_chunks: Integer,                   // 总子块数
        processed_chunks: Integer                // 已处理子块数
    },
    estimated_completion_time: DateTime          // 预估完成时间
}
```

#### TexProcessingProgress (tex_processing_progress集合)
```
{
    _id: ObjectId,                               // 记录ID
    task_id: String,                             // 关联的任务ID
    processed_chunks: Integer,                   // 已处理子块数
    total_chunks: Integer,                       // 总子块数
    last_update: DateTime                        // 最后更新时间
}
```

#### QAGeneration (qa_generations集合)
```
{
    _id: ObjectId,                               // 记录ID
    input_file: String,                          // 输入文件名
    original_file: String,                       // 原始文件名
    model_name: String,                          // 使用的模型名称
    domain: String,                              // 领域
    status: String,                              // 状态(processing/completed/failed/timeout/aborted/overwritten)
    source_text: String,                         // 源文本
    content: String,                             // QA对JSON字符串
    progress: Integer,                           // 进度百分比(0-100)
    start_time: DateTime,                        // 开始时间
    created_at: DateTime,                        // 创建时间
    error_message: String,                       // 错误信息
    chunk_info: {                                // 分块信息
        total_chunks: Integer,                   // 总子块数
        processed_chunks: Integer                // 已处理子块数
    },
    estimated_completion_time: DateTime          // 预估完成时间
}
```

#### QAPairDB (qa_pairs集合)
```
{
    _id: ObjectId,                               // 记录ID
    generation_id: ObjectId,                     // 关联的生成记录ID
    question: String,                            // 问题
    answer: String                               // 回答
}
```

#### DatasetEntry (dataset_entries集合)
```
{
    _id: ObjectId,                               // 数据集ID
    name: String,                                // 数据集名称
    description: String,                         // 描述
    pool_id: Integer,                            // 池ID
    kind: Integer,                               // 类型
    file_data: String,                           // 数据内容(JSON字符串)
    created_at: DateTime,                        // 创建时间
    model_name: String,                          // 使用的模型名称
    domain: String,                              // 领域
    is_qa: Boolean                               // 是否为QA数据集
}
```

#### ErrorLog (error_logs集合)
```
{
    _id: ObjectId,                               // 日志ID
    timestamp: DateTime,                         // 时间戳
    error_message: String,                       // 错误消息
    source: String,                              // 错误来源
    stack_trace: String                          // 堆栈跟踪
}
```

## 3. 进展情况

目前已完成的主要模块有：

1. **文件上传与管理模块**
   - 实现了文本文件和二进制文件的上传接口
   - 完成了文件列表查询、删除和内容预览功能
   - 实现了文件状态的完整生命周期管理

2. **文本解析模块**
   - 完成了针对各种文件类型的解析处理
   - 实现了解析进度的实时跟踪
   - 实现了解析结果的存储和查询接口

3. **LaTeX转换模块**
   - 实现了文本到LaTeX的并行转换
   - 完成了进度跟踪和完成时间预估
   - 实现了任务中止功能

4. **问答对生成模块**
   - 完成了基于LaTeX内容的问答对生成
   - 实现了多API并行生成和进度跟踪
   - 完成了生成结果的存储和查询接口

## 4. 	

{"version": 3, "sources": ["../../svelte/src/internal/shared/utils.js", "../../svelte/src/internal/client/constants.js", "../../svelte/src/internal/client/errors.js", "../../svelte/src/constants.js", "../../svelte/src/internal/shared/warnings.js", "../../svelte/src/internal/shared/clone.js", "../../svelte/src/internal/client/dom/task.js", "../../svelte/src/internal/client/dev/ownership.js", "../../svelte/src/internal/client/reactivity/equality.js", "../../svelte/src/internal/client/reactivity/sources.js", "../../svelte/src/internal/client/reactivity/deriveds.js", "../../svelte/src/internal/shared/errors.js", "../../svelte/src/internal/client/runtime.js", "../../svelte/src/internal/client/dom/hydration.js", "../../svelte/src/internal/client/proxy.js", "../../svelte/src/internal/client/dev/equality.js", "../../svelte/src/internal/client/dom/operations.js", "../../svelte/src/internal/client/reactivity/effects.js", "../../svelte/src/internal/client/dev/tracing.js", "../../svelte/src/internal/client/dom/elements/misc.js", "../../svelte/src/internal/client/dom/elements/bindings/shared.js", "../../svelte/src/internal/client/dom/elements/events.js"], "sourcesContent": ["// Store the references to globals in case someone tries to monkey patch these, causing the below\n// to de-opt (this occurs often when using popular extensions).\nexport var is_array = Array.isArray;\nexport var index_of = Array.prototype.indexOf;\nexport var array_from = Array.from;\nexport var object_keys = Object.keys;\nexport var define_property = Object.defineProperty;\nexport var get_descriptor = Object.getOwnPropertyDescriptor;\nexport var get_descriptors = Object.getOwnPropertyDescriptors;\nexport var object_prototype = Object.prototype;\nexport var array_prototype = Array.prototype;\nexport var get_prototype_of = Object.getPrototypeOf;\n\n/**\n * @param {any} thing\n * @returns {thing is Function}\n */\nexport function is_function(thing) {\n\treturn typeof thing === 'function';\n}\n\nexport const noop = () => {};\n\n// Adapted from https://github.com/then/is-promise/blob/master/index.js\n// Distributed under MIT License https://github.com/then/is-promise/blob/master/LICENSE\n\n/**\n * @template [T=any]\n * @param {any} value\n * @returns {value is PromiseLike<T>}\n */\nexport function is_promise(value) {\n\treturn typeof value?.then === 'function';\n}\n\n/** @param {Function} fn */\nexport function run(fn) {\n\treturn fn();\n}\n\n/** @param {Array<() => void>} arr */\nexport function run_all(arr) {\n\tfor (var i = 0; i < arr.length; i++) {\n\t\tarr[i]();\n\t}\n}\n\n/**\n * TODO replace with Promise.withResolvers once supported widely enough\n * @template T\n */\nexport function deferred() {\n\t/** @type {(value: T) => void} */\n\tvar resolve;\n\n\t/** @type {(reason: any) => void} */\n\tvar reject;\n\n\t/** @type {Promise<T>} */\n\tvar promise = new Promise((res, rej) => {\n\t\tresolve = res;\n\t\treject = rej;\n\t});\n\n\t// @ts-expect-error\n\treturn { promise, resolve, reject };\n}\n\n/**\n * @template V\n * @param {V} value\n * @param {V | (() => V)} fallback\n * @param {boolean} [lazy]\n * @returns {V}\n */\nexport function fallback(value, fallback, lazy = false) {\n\treturn value === undefined\n\t\t? lazy\n\t\t\t? /** @type {() => V} */ (fallback)()\n\t\t\t: /** @type {V} */ (fallback)\n\t\t: value;\n}\n", "export const DERIVED = 1 << 1;\nexport const EFFECT = 1 << 2;\nexport const RENDER_EFFECT = 1 << 3;\nexport const BLOCK_EFFECT = 1 << 4;\nexport const BRANCH_EFFECT = 1 << 5;\nexport const ROOT_EFFECT = 1 << 6;\nexport const BOUNDARY_EFFECT = 1 << 7;\nexport const UNOWNED = 1 << 8;\nexport const DISCONNECTED = 1 << 9;\nexport const CLEAN = 1 << 10;\nexport const DIRTY = 1 << 11;\nexport const MAYBE_DIRTY = 1 << 12;\nexport const INERT = 1 << 13;\nexport const DESTROYED = 1 << 14;\nexport const EFFECT_RAN = 1 << 15;\n/** 'Transparent' effects do not create a transition boundary */\nexport const EFFECT_TRANSPARENT = 1 << 16;\n/** Svelte 4 legacy mode props need to be handled with deriveds and be recognized elsewhere, hence the dedicated flag */\nexport const LEGACY_DERIVED_PROP = 1 << 17;\nexport const INSPECT_EFFECT = 1 << 18;\nexport const HEAD_EFFECT = 1 << 19;\nexport const EFFECT_HAS_DERIVED = 1 << 20;\n\nexport const STATE_SYMBOL = Symbol('$state');\nexport const STATE_SYMBOL_METADATA = Symbol('$state metadata');\nexport const LEGACY_PROPS = Symbol('legacy props');\nexport const LOADING_ATTR_SYMBOL = Symbol('');\n", "/* This file is generated by scripts/process-messages/index.js. Do not edit! */\n\nimport { DEV } from 'esm-env';\n\n/**\n * Using `bind:value` together with a checkbox input is not allowed. Use `bind:checked` instead\n * @returns {never}\n */\nexport function bind_invalid_checkbox_value() {\n\tif (DEV) {\n\t\tconst error = new Error(`bind_invalid_checkbox_value\\nUsing \\`bind:value\\` together with a checkbox input is not allowed. Use \\`bind:checked\\` instead\\nhttps://svelte.dev/e/bind_invalid_checkbox_value`);\n\n\t\terror.name = 'Svelte error';\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/bind_invalid_checkbox_value`);\n\t}\n}\n\n/**\n * Component %component% has an export named `%key%` that a consumer component is trying to access using `bind:%key%`, which is disallowed. Instead, use `bind:this` (e.g. `<%name% bind:this={component} />`) and then access the property on the bound component instance (e.g. `component.%key%`)\n * @param {string} component\n * @param {string} key\n * @param {string} name\n * @returns {never}\n */\nexport function bind_invalid_export(component, key, name) {\n\tif (DEV) {\n\t\tconst error = new Error(`bind_invalid_export\\nComponent ${component} has an export named \\`${key}\\` that a consumer component is trying to access using \\`bind:${key}\\`, which is disallowed. Instead, use \\`bind:this\\` (e.g. \\`<${name} bind:this={component} />\\`) and then access the property on the bound component instance (e.g. \\`component.${key}\\`)\\nhttps://svelte.dev/e/bind_invalid_export`);\n\n\t\terror.name = 'Svelte error';\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/bind_invalid_export`);\n\t}\n}\n\n/**\n * A component is attempting to bind to a non-bindable property `%key%` belonging to %component% (i.e. `<%name% bind:%key%={...}>`). To mark a property as bindable: `let { %key% = $bindable() } = $props()`\n * @param {string} key\n * @param {string} component\n * @param {string} name\n * @returns {never}\n */\nexport function bind_not_bindable(key, component, name) {\n\tif (DEV) {\n\t\tconst error = new Error(`bind_not_bindable\\nA component is attempting to bind to a non-bindable property \\`${key}\\` belonging to ${component} (i.e. \\`<${name} bind:${key}={...}>\\`). To mark a property as bindable: \\`let { ${key} = $bindable() } = $props()\\`\\nhttps://svelte.dev/e/bind_not_bindable`);\n\n\t\terror.name = 'Svelte error';\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/bind_not_bindable`);\n\t}\n}\n\n/**\n * %parent% called `%method%` on an instance of %component%, which is no longer valid in Svelte 5\n * @param {string} parent\n * @param {string} method\n * @param {string} component\n * @returns {never}\n */\nexport function component_api_changed(parent, method, component) {\n\tif (DEV) {\n\t\tconst error = new Error(`component_api_changed\\n${parent} called \\`${method}\\` on an instance of ${component}, which is no longer valid in Svelte 5\\nhttps://svelte.dev/e/component_api_changed`);\n\n\t\terror.name = 'Svelte error';\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/component_api_changed`);\n\t}\n}\n\n/**\n * Attempted to instantiate %component% with `new %name%`, which is no longer valid in Svelte 5. If this component is not under your control, set the `compatibility.componentApi` compiler option to `4` to keep it working.\n * @param {string} component\n * @param {string} name\n * @returns {never}\n */\nexport function component_api_invalid_new(component, name) {\n\tif (DEV) {\n\t\tconst error = new Error(`component_api_invalid_new\\nAttempted to instantiate ${component} with \\`new ${name}\\`, which is no longer valid in Svelte 5. If this component is not under your control, set the \\`compatibility.componentApi\\` compiler option to \\`4\\` to keep it working.\\nhttps://svelte.dev/e/component_api_invalid_new`);\n\n\t\terror.name = 'Svelte error';\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/component_api_invalid_new`);\n\t}\n}\n\n/**\n * A derived value cannot reference itself recursively\n * @returns {never}\n */\nexport function derived_references_self() {\n\tif (DEV) {\n\t\tconst error = new Error(`derived_references_self\\nA derived value cannot reference itself recursively\\nhttps://svelte.dev/e/derived_references_self`);\n\n\t\terror.name = 'Svelte error';\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/derived_references_self`);\n\t}\n}\n\n/**\n * Keyed each block has duplicate key `%value%` at indexes %a% and %b%\n * @param {string} a\n * @param {string} b\n * @param {string | undefined | null} [value]\n * @returns {never}\n */\nexport function each_key_duplicate(a, b, value) {\n\tif (DEV) {\n\t\tconst error = new Error(`each_key_duplicate\\n${value ? `Keyed each block has duplicate key \\`${value}\\` at indexes ${a} and ${b}` : `Keyed each block has duplicate key at indexes ${a} and ${b}`}\\nhttps://svelte.dev/e/each_key_duplicate`);\n\n\t\terror.name = 'Svelte error';\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/each_key_duplicate`);\n\t}\n}\n\n/**\n * `%rune%` cannot be used inside an effect cleanup function\n * @param {string} rune\n * @returns {never}\n */\nexport function effect_in_teardown(rune) {\n\tif (DEV) {\n\t\tconst error = new Error(`effect_in_teardown\\n\\`${rune}\\` cannot be used inside an effect cleanup function\\nhttps://svelte.dev/e/effect_in_teardown`);\n\n\t\terror.name = 'Svelte error';\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/effect_in_teardown`);\n\t}\n}\n\n/**\n * Effect cannot be created inside a `$derived` value that was not itself created inside an effect\n * @returns {never}\n */\nexport function effect_in_unowned_derived() {\n\tif (DEV) {\n\t\tconst error = new Error(`effect_in_unowned_derived\\nEffect cannot be created inside a \\`$derived\\` value that was not itself created inside an effect\\nhttps://svelte.dev/e/effect_in_unowned_derived`);\n\n\t\terror.name = 'Svelte error';\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/effect_in_unowned_derived`);\n\t}\n}\n\n/**\n * `%rune%` can only be used inside an effect (e.g. during component initialisation)\n * @param {string} rune\n * @returns {never}\n */\nexport function effect_orphan(rune) {\n\tif (DEV) {\n\t\tconst error = new Error(`effect_orphan\\n\\`${rune}\\` can only be used inside an effect (e.g. during component initialisation)\\nhttps://svelte.dev/e/effect_orphan`);\n\n\t\terror.name = 'Svelte error';\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/effect_orphan`);\n\t}\n}\n\n/**\n * Maximum update depth exceeded. This can happen when a reactive block or effect repeatedly sets a new value. Svelte limits the number of nested updates to prevent infinite loops\n * @returns {never}\n */\nexport function effect_update_depth_exceeded() {\n\tif (DEV) {\n\t\tconst error = new Error(`effect_update_depth_exceeded\\nMaximum update depth exceeded. This can happen when a reactive block or effect repeatedly sets a new value. Svelte limits the number of nested updates to prevent infinite loops\\nhttps://svelte.dev/e/effect_update_depth_exceeded`);\n\n\t\terror.name = 'Svelte error';\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/effect_update_depth_exceeded`);\n\t}\n}\n\n/**\n * Failed to hydrate the application\n * @returns {never}\n */\nexport function hydration_failed() {\n\tif (DEV) {\n\t\tconst error = new Error(`hydration_failed\\nFailed to hydrate the application\\nhttps://svelte.dev/e/hydration_failed`);\n\n\t\terror.name = 'Svelte error';\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/hydration_failed`);\n\t}\n}\n\n/**\n * Could not `{@render}` snippet due to the expression being `null` or `undefined`. Consider using optional chaining `{@render snippet?.()}`\n * @returns {never}\n */\nexport function invalid_snippet() {\n\tif (DEV) {\n\t\tconst error = new Error(`invalid_snippet\\nCould not \\`{@render}\\` snippet due to the expression being \\`null\\` or \\`undefined\\`. Consider using optional chaining \\`{@render snippet?.()}\\`\\nhttps://svelte.dev/e/invalid_snippet`);\n\n\t\terror.name = 'Svelte error';\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/invalid_snippet`);\n\t}\n}\n\n/**\n * `%name%(...)` cannot be used in runes mode\n * @param {string} name\n * @returns {never}\n */\nexport function lifecycle_legacy_only(name) {\n\tif (DEV) {\n\t\tconst error = new Error(`lifecycle_legacy_only\\n\\`${name}(...)\\` cannot be used in runes mode\\nhttps://svelte.dev/e/lifecycle_legacy_only`);\n\n\t\terror.name = 'Svelte error';\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/lifecycle_legacy_only`);\n\t}\n}\n\n/**\n * Cannot do `bind:%key%={undefined}` when `%key%` has a fallback value\n * @param {string} key\n * @returns {never}\n */\nexport function props_invalid_value(key) {\n\tif (DEV) {\n\t\tconst error = new Error(`props_invalid_value\\nCannot do \\`bind:${key}={undefined}\\` when \\`${key}\\` has a fallback value\\nhttps://svelte.dev/e/props_invalid_value`);\n\n\t\terror.name = 'Svelte error';\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/props_invalid_value`);\n\t}\n}\n\n/**\n * Rest element properties of `$props()` such as `%property%` are readonly\n * @param {string} property\n * @returns {never}\n */\nexport function props_rest_readonly(property) {\n\tif (DEV) {\n\t\tconst error = new Error(`props_rest_readonly\\nRest element properties of \\`$props()\\` such as \\`${property}\\` are readonly\\nhttps://svelte.dev/e/props_rest_readonly`);\n\n\t\terror.name = 'Svelte error';\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/props_rest_readonly`);\n\t}\n}\n\n/**\n * The `%rune%` rune is only available inside `.svelte` and `.svelte.js/ts` files\n * @param {string} rune\n * @returns {never}\n */\nexport function rune_outside_svelte(rune) {\n\tif (DEV) {\n\t\tconst error = new Error(`rune_outside_svelte\\nThe \\`${rune}\\` rune is only available inside \\`.svelte\\` and \\`.svelte.js/ts\\` files\\nhttps://svelte.dev/e/rune_outside_svelte`);\n\n\t\terror.name = 'Svelte error';\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/rune_outside_svelte`);\n\t}\n}\n\n/**\n * Property descriptors defined on `$state` objects must contain `value` and always be `enumerable`, `configurable` and `writable`.\n * @returns {never}\n */\nexport function state_descriptors_fixed() {\n\tif (DEV) {\n\t\tconst error = new Error(`state_descriptors_fixed\\nProperty descriptors defined on \\`$state\\` objects must contain \\`value\\` and always be \\`enumerable\\`, \\`configurable\\` and \\`writable\\`.\\nhttps://svelte.dev/e/state_descriptors_fixed`);\n\n\t\terror.name = 'Svelte error';\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/state_descriptors_fixed`);\n\t}\n}\n\n/**\n * Cannot set prototype of `$state` object\n * @returns {never}\n */\nexport function state_prototype_fixed() {\n\tif (DEV) {\n\t\tconst error = new Error(`state_prototype_fixed\\nCannot set prototype of \\`$state\\` object\\nhttps://svelte.dev/e/state_prototype_fixed`);\n\n\t\terror.name = 'Svelte error';\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/state_prototype_fixed`);\n\t}\n}\n\n/**\n * Reading state that was created inside the same derived is forbidden. Consider using `untrack` to read locally created state\n * @returns {never}\n */\nexport function state_unsafe_local_read() {\n\tif (DEV) {\n\t\tconst error = new Error(`state_unsafe_local_read\\nReading state that was created inside the same derived is forbidden. Consider using \\`untrack\\` to read locally created state\\nhttps://svelte.dev/e/state_unsafe_local_read`);\n\n\t\terror.name = 'Svelte error';\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/state_unsafe_local_read`);\n\t}\n}\n\n/**\n * Updating state inside a derived or a template expression is forbidden. If the value should not be reactive, declare it without `$state`\n * @returns {never}\n */\nexport function state_unsafe_mutation() {\n\tif (DEV) {\n\t\tconst error = new Error(`state_unsafe_mutation\\nUpdating state inside a derived or a template expression is forbidden. If the value should not be reactive, declare it without \\`$state\\`\\nhttps://svelte.dev/e/state_unsafe_mutation`);\n\n\t\terror.name = 'Svelte error';\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/state_unsafe_mutation`);\n\t}\n}", "export const EACH_ITEM_REACTIVE = 1;\nexport const EACH_INDEX_REACTIVE = 1 << 1;\n/** See EachBlock interface metadata.is_controlled for an explanation what this is */\nexport const EACH_IS_CONTROLLED = 1 << 2;\nexport const EACH_IS_ANIMATED = 1 << 3;\nexport const EACH_ITEM_IMMUTABLE = 1 << 4;\n\nexport const PROPS_IS_IMMUTABLE = 1;\nexport const PROPS_IS_RUNES = 1 << 1;\nexport const PROPS_IS_UPDATED = 1 << 2;\nexport const PROPS_IS_BINDABLE = 1 << 3;\nexport const PROPS_IS_LAZY_INITIAL = 1 << 4;\n\nexport const TRANSITION_IN = 1;\nexport const TRANSITION_OUT = 1 << 1;\nexport const TRANSITION_GLOBAL = 1 << 2;\n\nexport const TEMPLATE_FRAGMENT = 1;\nexport const TEMPLATE_USE_IMPORT_NODE = 1 << 1;\n\nexport const HYDRATION_START = '[';\n/** used to indicate that an `{:else}...` block was rendered */\nexport const HYDRATION_START_ELSE = '[!';\nexport const HYDRATION_END = ']';\nexport const HYDRATION_ERROR = {};\n\nexport const ELEMENT_IS_NAMESPACED = 1;\nexport const ELEMENT_PRESERVE_ATTRIBUTE_CASE = 1 << 1;\n\nexport const UNINITIALIZED = Symbol();\n\n// Dev-time component properties\nexport const FILENAME = Symbol('filename');\nexport const HMR = Symbol('hmr');\n\nexport const NAMESPACE_SVG = 'http://www.w3.org/2000/svg';\nexport const NAMESPACE_MATHML = 'http://www.w3.org/1998/Math/MathML';\n\n// we use a list of ignorable runtime warnings because not every runtime warning\n// can be ignored and we want to keep the validation for svelte-ignore in place\nexport const IGNORABLE_RUNTIME_WARNINGS = /** @type {const} */ ([\n\t'state_snapshot_uncloneable',\n\t'binding_property_non_reactive',\n\t'hydration_attribute_changed',\n\t'hydration_html_changed',\n\t'ownership_invalid_binding',\n\t'ownership_invalid_mutation'\n]);\n\n/**\n * Whitespace inside one of these elements will not result in\n * a whitespace node being created in any circumstances. (This\n * list is almost certainly very incomplete)\n * TODO this is currently unused\n */\nexport const ELEMENTS_WITHOUT_TEXT = ['audio', 'datalist', 'dl', 'optgroup', 'select', 'video'];\n", "/* This file is generated by scripts/process-messages/index.js. Do not edit! */\n\nimport { DEV } from 'esm-env';\n\nvar bold = 'font-weight: bold';\nvar normal = 'font-weight: normal';\n\n/**\n * `<svelte:element this=\"%tag%\">` is a void element — it cannot have content\n * @param {string} tag\n */\nexport function dynamic_void_element_content(tag) {\n\tif (DEV) {\n\t\tconsole.warn(`%c[svelte] dynamic_void_element_content\\n%c\\`<svelte:element this=\"${tag}\">\\` is a void element — it cannot have content\\nhttps://svelte.dev/e/dynamic_void_element_content`, bold, normal);\n\t} else {\n\t\tconsole.warn(`https://svelte.dev/e/dynamic_void_element_content`);\n\t}\n}\n\n/**\n * The following properties cannot be cloned with `$state.snapshot` — the return value contains the originals:\n * \n * %properties%\n * @param {string | undefined | null} [properties]\n */\nexport function state_snapshot_uncloneable(properties) {\n\tif (DEV) {\n\t\tconsole.warn(`%c[svelte] state_snapshot_uncloneable\\n%c${properties\n\t\t\t? `The following properties cannot be cloned with \\`$state.snapshot\\` — the return value contains the originals:\n\n${properties}`\n\t\t\t: 'Value cannot be cloned with `$state.snapshot` — the original value was returned'}\\nhttps://svelte.dev/e/state_snapshot_uncloneable`, bold, normal);\n\t} else {\n\t\tconsole.warn(`https://svelte.dev/e/state_snapshot_uncloneable`);\n\t}\n}", "/** @import { Snapshot } from './types' */\nimport { DEV } from 'esm-env';\nimport * as w from './warnings.js';\nimport { get_prototype_of, is_array, object_prototype } from './utils.js';\n\n/**\n * In dev, we keep track of which properties could not be cloned. In prod\n * we don't bother, but we keep a dummy array around so that the\n * signature stays the same\n * @type {string[]}\n */\nconst empty = [];\n\n/**\n * @template T\n * @param {T} value\n * @param {boolean} [skip_warning]\n * @returns {Snapshot<T>}\n */\nexport function snapshot(value, skip_warning = false) {\n\tif (DEV && !skip_warning) {\n\t\t/** @type {string[]} */\n\t\tconst paths = [];\n\n\t\tconst copy = clone(value, new Map(), '', paths);\n\t\tif (paths.length === 1 && paths[0] === '') {\n\t\t\t// value could not be cloned\n\t\t\tw.state_snapshot_uncloneable();\n\t\t} else if (paths.length > 0) {\n\t\t\t// some properties could not be cloned\n\t\t\tconst slice = paths.length > 10 ? paths.slice(0, 7) : paths.slice(0, 10);\n\t\t\tconst excess = paths.length - slice.length;\n\n\t\t\tlet uncloned = slice.map((path) => `- <value>${path}`).join('\\n');\n\t\t\tif (excess > 0) uncloned += `\\n- ...and ${excess} more`;\n\n\t\t\tw.state_snapshot_uncloneable(uncloned);\n\t\t}\n\n\t\treturn copy;\n\t}\n\n\treturn clone(value, new Map(), '', empty);\n}\n\n/**\n * @template T\n * @param {T} value\n * @param {Map<T, Snapshot<T>>} cloned\n * @param {string} path\n * @param {string[]} paths\n * @param {null | T} original The original value, if `value` was produced from a `toJSON` call\n * @returns {Snapshot<T>}\n */\nfunction clone(value, cloned, path, paths, original = null) {\n\tif (typeof value === 'object' && value !== null) {\n\t\tvar unwrapped = cloned.get(value);\n\t\tif (unwrapped !== undefined) return unwrapped;\n\n\t\tif (value instanceof Map) return /** @type {Snapshot<T>} */ (new Map(value));\n\t\tif (value instanceof Set) return /** @type {Snapshot<T>} */ (new Set(value));\n\n\t\tif (is_array(value)) {\n\t\t\tvar copy = /** @type {Snapshot<any>} */ (Array(value.length));\n\t\t\tcloned.set(value, copy);\n\n\t\t\tif (original !== null) {\n\t\t\t\tcloned.set(original, copy);\n\t\t\t}\n\n\t\t\tfor (var i = 0; i < value.length; i += 1) {\n\t\t\t\tvar element = value[i];\n\t\t\t\tif (i in value) {\n\t\t\t\t\tcopy[i] = clone(element, cloned, DEV ? `${path}[${i}]` : path, paths);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\treturn copy;\n\t\t}\n\n\t\tif (get_prototype_of(value) === object_prototype) {\n\t\t\t/** @type {Snapshot<any>} */\n\t\t\tcopy = {};\n\t\t\tcloned.set(value, copy);\n\n\t\t\tif (original !== null) {\n\t\t\t\tcloned.set(original, copy);\n\t\t\t}\n\n\t\t\tfor (var key in value) {\n\t\t\t\t// @ts-expect-error\n\t\t\t\tcopy[key] = clone(value[key], cloned, DEV ? `${path}.${key}` : path, paths);\n\t\t\t}\n\n\t\t\treturn copy;\n\t\t}\n\n\t\tif (value instanceof Date) {\n\t\t\treturn /** @type {Snapshot<T>} */ (structuredClone(value));\n\t\t}\n\n\t\tif (typeof (/** @type {T & { toJSON?: any } } */ (value).toJSON) === 'function') {\n\t\t\treturn clone(\n\t\t\t\t/** @type {T & { toJSON(): any } } */ (value).toJSON(),\n\t\t\t\tcloned,\n\t\t\t\tDEV ? `${path}.toJSON()` : path,\n\t\t\t\tpaths,\n\t\t\t\t// Associate the instance with the toJSON clone\n\t\t\t\tvalue\n\t\t\t);\n\t\t}\n\t}\n\n\tif (value instanceof EventTarget) {\n\t\t// can't be cloned\n\t\treturn /** @type {Snapshot<T>} */ (value);\n\t}\n\n\ttry {\n\t\treturn /** @type {Snapshot<T>} */ (structuredClone(value));\n\t} catch (e) {\n\t\tif (DEV) {\n\t\t\tpaths.push(path);\n\t\t}\n\n\t\treturn /** @type {Snapshot<T>} */ (value);\n\t}\n}\n", "import { run_all } from '../../shared/utils.js';\n\n// Fallback for when requestIdleCallback is not available\nexport const request_idle_callback =\n\ttypeof requestIdleCallback === 'undefined'\n\t\t? (/** @type {() => void} */ cb) => setTimeout(cb, 1)\n\t\t: requestIdleCallback;\n\nlet is_micro_task_queued = false;\nlet is_idle_task_queued = false;\n\n/** @type {Array<() => void>} */\nlet current_queued_micro_tasks = [];\n/** @type {Array<() => void>} */\nlet current_queued_idle_tasks = [];\n\nfunction process_micro_tasks() {\n\tis_micro_task_queued = false;\n\tconst tasks = current_queued_micro_tasks.slice();\n\tcurrent_queued_micro_tasks = [];\n\trun_all(tasks);\n}\n\nfunction process_idle_tasks() {\n\tis_idle_task_queued = false;\n\tconst tasks = current_queued_idle_tasks.slice();\n\tcurrent_queued_idle_tasks = [];\n\trun_all(tasks);\n}\n\n/**\n * @param {() => void} fn\n */\nexport function queue_micro_task(fn) {\n\tif (!is_micro_task_queued) {\n\t\tis_micro_task_queued = true;\n\t\tqueueMicrotask(process_micro_tasks);\n\t}\n\tcurrent_queued_micro_tasks.push(fn);\n}\n\n/**\n * @param {() => void} fn\n */\nexport function queue_idle_task(fn) {\n\tif (!is_idle_task_queued) {\n\t\tis_idle_task_queued = true;\n\t\trequest_idle_callback(process_idle_tasks);\n\t}\n\tcurrent_queued_idle_tasks.push(fn);\n}\n\n/**\n * Synchronously run any queued tasks.\n */\nexport function flush_tasks() {\n\tif (is_micro_task_queued) {\n\t\tprocess_micro_tasks();\n\t}\n\tif (is_idle_task_queued) {\n\t\tprocess_idle_tasks();\n\t}\n}\n", "/** @import { ProxyMetadata } from '#client' */\n/** @typedef {{ file: string, line: number, column: number }} Location */\n\nimport { STATE_SYMBOL_METADATA } from '../constants.js';\nimport { render_effect, user_pre_effect } from '../reactivity/effects.js';\nimport { dev_current_component_function } from '../runtime.js';\nimport { get_prototype_of } from '../../shared/utils.js';\nimport * as w from '../warnings.js';\nimport { FILENAME } from '../../../constants.js';\n\n/** @type {Record<string, Array<{ start: Location, end: Location, component: Function }>>} */\nconst boundaries = {};\n\nconst chrome_pattern = /at (?:.+ \\()?(.+):(\\d+):(\\d+)\\)?$/;\nconst firefox_pattern = /@(.+):(\\d+):(\\d+)$/;\n\nfunction get_stack() {\n\tconst stack = new Error().stack;\n\tif (!stack) return null;\n\n\tconst entries = [];\n\n\tfor (const line of stack.split('\\n')) {\n\t\tlet match = chrome_pattern.exec(line) ?? firefox_pattern.exec(line);\n\n\t\tif (match) {\n\t\t\tentries.push({\n\t\t\t\tfile: match[1],\n\t\t\t\tline: +match[2],\n\t\t\t\tcolumn: +match[3]\n\t\t\t});\n\t\t}\n\t}\n\n\treturn entries;\n}\n\n/**\n * Determines which `.svelte` component is responsible for a given state change\n * @returns {Function | null}\n */\nexport function get_component() {\n\t// first 4 lines are svelte internals; adjust this number if we change the internal call stack\n\tconst stack = get_stack()?.slice(4);\n\tif (!stack) return null;\n\n\tfor (let i = 0; i < stack.length; i++) {\n\t\tconst entry = stack[i];\n\t\tconst modules = boundaries[entry.file];\n\t\tif (!modules) {\n\t\t\t// If the first entry is not a component, that means the modification very likely happened\n\t\t\t// within a .svelte.js file, possibly triggered by a component. Since these files are not part\n\t\t\t// of the bondaries/component context heuristic, we need to bail in this case, else we would\n\t\t\t// have false positives when the .svelte.ts file provides a state creator function, encapsulating\n\t\t\t// the state and its mutations, and is being called from a component other than the one who\n\t\t\t// called the state creator function.\n\t\t\tif (i === 0) return null;\n\t\t\tcontinue;\n\t\t}\n\n\t\tfor (const module of modules) {\n\t\t\tif (module.end == null) {\n\t\t\t\treturn null;\n\t\t\t}\n\t\t\tif (module.start.line < entry.line && module.end.line > entry.line) {\n\t\t\t\treturn module.component;\n\t\t\t}\n\t\t}\n\t}\n\n\treturn null;\n}\n\nexport const ADD_OWNER = Symbol('ADD_OWNER');\n\n/**\n * Together with `mark_module_end`, this function establishes the boundaries of a `.svelte` file,\n * such that subsequent calls to `get_component` can tell us which component is responsible\n * for a given state change\n */\nexport function mark_module_start() {\n\tconst start = get_stack()?.[2];\n\n\tif (start) {\n\t\t(boundaries[start.file] ??= []).push({\n\t\t\tstart,\n\t\t\t// @ts-expect-error\n\t\t\tend: null,\n\t\t\t// @ts-expect-error we add the component at the end, since HMR will overwrite the function\n\t\t\tcomponent: null\n\t\t});\n\t}\n}\n\n/**\n * @param {Function} component\n */\nexport function mark_module_end(component) {\n\tconst end = get_stack()?.[2];\n\n\tif (end) {\n\t\tconst boundaries_file = boundaries[end.file];\n\t\tconst boundary = boundaries_file[boundaries_file.length - 1];\n\n\t\tboundary.end = end;\n\t\tboundary.component = component;\n\t}\n}\n\n/**\n * @param {any} object\n * @param {any} owner\n * @param {boolean} [global]\n * @param {boolean} [skip_warning]\n */\nexport function add_owner(object, owner, global = false, skip_warning = false) {\n\tif (object && !global) {\n\t\tconst component = dev_current_component_function;\n\t\tconst metadata = object[STATE_SYMBOL_METADATA];\n\t\tif (metadata && !has_owner(metadata, component)) {\n\t\t\tlet original = get_owner(metadata);\n\n\t\t\tif (owner[FILENAME] !== component[FILENAME] && !skip_warning) {\n\t\t\t\tw.ownership_invalid_binding(component[FILENAME], owner[FILENAME], original[FILENAME]);\n\t\t\t}\n\t\t}\n\t}\n\n\tadd_owner_to_object(object, owner, new Set());\n}\n\n/**\n * @param {() => unknown} get_object\n * @param {any} Component\n * @param {boolean} [skip_warning]\n */\nexport function add_owner_effect(get_object, Component, skip_warning = false) {\n\tuser_pre_effect(() => {\n\t\tadd_owner(get_object(), Component, false, skip_warning);\n\t});\n}\n\n/**\n * @param {ProxyMetadata | null} from\n * @param {ProxyMetadata} to\n */\nexport function widen_ownership(from, to) {\n\tif (to.owners === null) {\n\t\treturn;\n\t}\n\n\twhile (from) {\n\t\tif (from.owners === null) {\n\t\t\tto.owners = null;\n\t\t\tbreak;\n\t\t}\n\n\t\tfor (const owner of from.owners) {\n\t\t\tto.owners.add(owner);\n\t\t}\n\n\t\tfrom = from.parent;\n\t}\n}\n\n/**\n * @param {any} object\n * @param {Function} owner\n * @param {Set<any>} seen\n */\nfunction add_owner_to_object(object, owner, seen) {\n\tconst metadata = /** @type {ProxyMetadata} */ (object?.[STATE_SYMBOL_METADATA]);\n\n\tif (metadata) {\n\t\t// this is a state proxy, add owner directly, if not globally shared\n\t\tif ('owners' in metadata && metadata.owners != null) {\n\t\t\tmetadata.owners.add(owner);\n\t\t}\n\t} else if (object && typeof object === 'object') {\n\t\tif (seen.has(object)) return;\n\t\tseen.add(object);\n\t\tif (ADD_OWNER in object && object[ADD_OWNER]) {\n\t\t\t// this is a class with state fields. we put this in a render effect\n\t\t\t// so that if state is replaced (e.g. `instance.name = { first, last }`)\n\t\t\t// the new state is also co-owned by the caller of `getContext`\n\t\t\trender_effect(() => {\n\t\t\t\tobject[ADD_OWNER](owner);\n\t\t\t});\n\t\t} else {\n\t\t\tvar proto = get_prototype_of(object);\n\n\t\t\tif (proto === Object.prototype) {\n\t\t\t\t// recurse until we find a state proxy\n\t\t\t\tfor (const key in object) {\n\t\t\t\t\tadd_owner_to_object(object[key], owner, seen);\n\t\t\t\t}\n\t\t\t} else if (proto === Array.prototype) {\n\t\t\t\t// recurse until we find a state proxy\n\t\t\t\tfor (let i = 0; i < object.length; i += 1) {\n\t\t\t\t\tadd_owner_to_object(object[i], owner, seen);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n\n/**\n * @param {ProxyMetadata} metadata\n * @param {Function} component\n * @returns {boolean}\n */\nfunction has_owner(metadata, component) {\n\tif (metadata.owners === null) {\n\t\treturn true;\n\t}\n\n\treturn (\n\t\tmetadata.owners.has(component) ||\n\t\t(metadata.parent !== null && has_owner(metadata.parent, component))\n\t);\n}\n\n/**\n * @param {ProxyMetadata} metadata\n * @returns {any}\n */\nfunction get_owner(metadata) {\n\treturn (\n\t\tmetadata?.owners?.values().next().value ??\n\t\tget_owner(/** @type {ProxyMetadata} */ (metadata.parent))\n\t);\n}\n\nlet skip = false;\n\n/**\n * @param {() => any} fn\n */\nexport function skip_ownership_validation(fn) {\n\tskip = true;\n\tfn();\n\tskip = false;\n}\n\n/**\n * @param {ProxyMetadata} metadata\n */\nexport function check_ownership(metadata) {\n\tif (skip) return;\n\n\tconst component = get_component();\n\n\tif (component && !has_owner(metadata, component)) {\n\t\tlet original = get_owner(metadata);\n\n\t\t// @ts-expect-error\n\t\tif (original[FILENAME] !== component[FILENAME]) {\n\t\t\t// @ts-expect-error\n\t\t\tw.ownership_invalid_mutation(component[FILENAME], original[FILENAME]);\n\t\t} else {\n\t\t\tw.ownership_invalid_mutation();\n\t\t}\n\t}\n}\n", "/** @import { Equals } from '#client' */\n/** @type {Equals} */\nexport function equals(value) {\n\treturn value === this.v;\n}\n\n/**\n * @param {unknown} a\n * @param {unknown} b\n * @returns {boolean}\n */\nexport function safe_not_equal(a, b) {\n\treturn a != a\n\t\t? b == b\n\t\t: a !== b || (a !== null && typeof a === 'object') || typeof a === 'function';\n}\n\n/**\n * @param {unknown} a\n * @param {unknown} b\n * @returns {boolean}\n */\nexport function not_equal(a, b) {\n\treturn a !== b;\n}\n\n/** @type {Equals} */\nexport function safe_equals(value) {\n\treturn !safe_not_equal(value, this.v);\n}\n", "/** @import { Derived, Effect, Reaction, Source, Value } from '#client' */\nimport { DEV } from 'esm-env';\nimport {\n\tcomponent_context,\n\tactive_reaction,\n\tactive_effect,\n\tuntracked_writes,\n\tget,\n\tis_runes,\n\tschedule_effect,\n\tset_untracked_writes,\n\tset_signal_status,\n\tuntrack,\n\tincrement_write_version,\n\tupdate_effect,\n\tderived_sources,\n\tset_derived_sources,\n\tcheck_dirtiness,\n\tset_is_flushing_effect,\n\tis_flushing_effect\n} from '../runtime.js';\nimport { equals, safe_equals } from './equality.js';\nimport {\n\tCLEAN,\n\tDERIVED,\n\tDIRTY,\n\tBRANCH_EFFECT,\n\tINSPECT_EFFECT,\n\tUNOWNED,\n\tMAYBE_DIRTY,\n\tBLOCK_EFFECT,\n\tROOT_EFFECT\n} from '../constants.js';\nimport * as e from '../errors.js';\nimport { legacy_mode_flag, tracing_mode_flag } from '../../flags/index.js';\nimport { get_stack } from '../dev/tracing.js';\n\nexport let inspect_effects = new Set();\n\n/**\n * @param {Set<any>} v\n */\nexport function set_inspect_effects(v) {\n\tinspect_effects = v;\n}\n\n/**\n * @template V\n * @param {V} v\n * @param {Error | null} [stack]\n * @returns {Source<V>}\n */\nexport function source(v, stack) {\n\t/** @type {Value} */\n\tvar signal = {\n\t\tf: 0, // TODO ideally we could skip this altogether, but it causes type errors\n\t\tv,\n\t\treactions: null,\n\t\tequals,\n\t\trv: 0,\n\t\twv: 0\n\t};\n\n\tif (DEV && tracing_mode_flag) {\n\t\tsignal.created = stack ?? get_stack('CreatedAt');\n\t\tsignal.debug = null;\n\t}\n\n\treturn signal;\n}\n\n/**\n * @template V\n * @param {V} v\n */\nexport function state(v) {\n\treturn push_derived_source(source(v));\n}\n\n/**\n * @template V\n * @param {V} initial_value\n * @param {boolean} [immutable]\n * @returns {Source<V>}\n */\n/*#__NO_SIDE_EFFECTS__*/\nexport function mutable_source(initial_value, immutable = false) {\n\tconst s = source(initial_value);\n\tif (!immutable) {\n\t\ts.equals = safe_equals;\n\t}\n\n\t// bind the signal to the component context, in case we need to\n\t// track updates to trigger beforeUpdate/afterUpdate callbacks\n\tif (legacy_mode_flag && component_context !== null && component_context.l !== null) {\n\t\t(component_context.l.s ??= []).push(s);\n\t}\n\n\treturn s;\n}\n\n/**\n * @template V\n * @param {V} v\n * @param {boolean} [immutable]\n * @returns {Source<V>}\n */\nexport function mutable_state(v, immutable = false) {\n\treturn push_derived_source(mutable_source(v, immutable));\n}\n\n/**\n * @template V\n * @param {Source<V>} source\n */\n/*#__NO_SIDE_EFFECTS__*/\nfunction push_derived_source(source) {\n\tif (active_reaction !== null && (active_reaction.f & DERIVED) !== 0) {\n\t\tif (derived_sources === null) {\n\t\t\tset_derived_sources([source]);\n\t\t} else {\n\t\t\tderived_sources.push(source);\n\t\t}\n\t}\n\n\treturn source;\n}\n\n/**\n * @template V\n * @param {Value<V>} source\n * @param {V} value\n */\nexport function mutate(source, value) {\n\tset(\n\t\tsource,\n\t\tuntrack(() => get(source))\n\t);\n\treturn value;\n}\n\n/**\n * @template V\n * @param {Source<V>} source\n * @param {V} value\n * @returns {V}\n */\nexport function set(source, value) {\n\tif (\n\t\tactive_reaction !== null &&\n\t\tis_runes() &&\n\t\t(active_reaction.f & (DERIVED | BLOCK_EFFECT)) !== 0 &&\n\t\t// If the source was created locally within the current derived, then\n\t\t// we allow the mutation.\n\t\t(derived_sources === null || !derived_sources.includes(source))\n\t) {\n\t\te.state_unsafe_mutation();\n\t}\n\n\treturn internal_set(source, value);\n}\n\n/**\n * @template V\n * @param {Source<V>} source\n * @param {V} value\n * @returns {V}\n */\nexport function internal_set(source, value) {\n\tif (!source.equals(value)) {\n\t\tvar old_value = source.v;\n\t\tsource.v = value;\n\t\tsource.wv = increment_write_version();\n\n\t\tif (DEV && tracing_mode_flag) {\n\t\t\tsource.updated = get_stack('UpdatedAt');\n\t\t\tif (active_effect != null) {\n\t\t\t\tsource.trace_need_increase = true;\n\t\t\t\tsource.trace_v ??= old_value;\n\t\t\t}\n\t\t}\n\n\t\tmark_reactions(source, DIRTY);\n\n\t\t// It's possible that the current reaction might not have up-to-date dependencies\n\t\t// whilst it's actively running. So in the case of ensuring it registers the reaction\n\t\t// properly for itself, we need to ensure the current effect actually gets\n\t\t// scheduled. i.e: `$effect(() => x++)`\n\t\tif (\n\t\t\tis_runes() &&\n\t\t\tactive_effect !== null &&\n\t\t\t(active_effect.f & CLEAN) !== 0 &&\n\t\t\t(active_effect.f & (BRANCH_EFFECT | ROOT_EFFECT)) === 0\n\t\t) {\n\t\t\tif (untracked_writes === null) {\n\t\t\t\tset_untracked_writes([source]);\n\t\t\t} else {\n\t\t\t\tuntracked_writes.push(source);\n\t\t\t}\n\t\t}\n\n\t\tif (DEV && inspect_effects.size > 0) {\n\t\t\tconst inspects = Array.from(inspect_effects);\n\t\t\tvar previously_flushing_effect = is_flushing_effect;\n\t\t\tset_is_flushing_effect(true);\n\t\t\ttry {\n\t\t\t\tfor (const effect of inspects) {\n\t\t\t\t\t// Mark clean inspect-effects as maybe dirty and then check their dirtiness\n\t\t\t\t\t// instead of just updating the effects - this way we avoid overfiring.\n\t\t\t\t\tif ((effect.f & CLEAN) !== 0) {\n\t\t\t\t\t\tset_signal_status(effect, MAYBE_DIRTY);\n\t\t\t\t\t}\n\t\t\t\t\tif (check_dirtiness(effect)) {\n\t\t\t\t\t\tupdate_effect(effect);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} finally {\n\t\t\t\tset_is_flushing_effect(previously_flushing_effect);\n\t\t\t}\n\t\t\tinspect_effects.clear();\n\t\t}\n\t}\n\n\treturn value;\n}\n\n/**\n * @param {Value} signal\n * @param {number} status should be DIRTY or MAYBE_DIRTY\n * @returns {void}\n */\nfunction mark_reactions(signal, status) {\n\tvar reactions = signal.reactions;\n\tif (reactions === null) return;\n\n\tvar runes = is_runes();\n\tvar length = reactions.length;\n\n\tfor (var i = 0; i < length; i++) {\n\t\tvar reaction = reactions[i];\n\t\tvar flags = reaction.f;\n\n\t\t// Skip any effects that are already dirty\n\t\tif ((flags & DIRTY) !== 0) continue;\n\n\t\t// In legacy mode, skip the current effect to prevent infinite loops\n\t\tif (!runes && reaction === active_effect) continue;\n\n\t\t// Inspect effects need to run immediately, so that the stack trace makes sense\n\t\tif (DEV && (flags & INSPECT_EFFECT) !== 0) {\n\t\t\tinspect_effects.add(reaction);\n\t\t\tcontinue;\n\t\t}\n\n\t\tset_signal_status(reaction, status);\n\n\t\t// If the signal a) was previously clean or b) is an unowned derived, then mark it\n\t\tif ((flags & (CLEAN | UNOWNED)) !== 0) {\n\t\t\tif ((flags & DERIVED) !== 0) {\n\t\t\t\tmark_reactions(/** @type {Derived} */ (reaction), MAYBE_DIRTY);\n\t\t\t} else {\n\t\t\t\tschedule_effect(/** @type {Effect} */ (reaction));\n\t\t\t}\n\t\t}\n\t}\n}\n", "/** @import { Derived, Effect } from '#client' */\nimport { DEV } from 'esm-env';\nimport {\n\tCLEAN,\n\tDERIVED,\n\tDESTROYED,\n\tDIRTY,\n\tEFFECT_HAS_DERIVED,\n\tMAYBE_DIRTY,\n\tUNOWNED\n} from '../constants.js';\nimport {\n\tactive_reaction,\n\tactive_effect,\n\tremove_reactions,\n\tset_signal_status,\n\tskip_reaction,\n\tupdate_reaction,\n\tincrement_write_version,\n\tset_active_effect,\n\tcomponent_context\n} from '../runtime.js';\nimport { equals, safe_equals } from './equality.js';\nimport * as e from '../errors.js';\nimport { destroy_effect } from './effects.js';\nimport { inspect_effects, set_inspect_effects } from './sources.js';\nimport { get_stack } from '../dev/tracing.js';\nimport { tracing_mode_flag } from '../../flags/index.js';\n\n/**\n * @template V\n * @param {() => V} fn\n * @returns {Derived<V>}\n */\n/*#__NO_SIDE_EFFECTS__*/\nexport function derived(fn) {\n\tvar flags = DERIVED | DIRTY;\n\n\tif (active_effect === null) {\n\t\tflags |= UNOWNED;\n\t} else {\n\t\t// Since deriveds are evaluated lazily, any effects created inside them are\n\t\t// created too late to ensure that the parent effect is added to the tree\n\t\tactive_effect.f |= EFFECT_HAS_DERIVED;\n\t}\n\n\tvar parent_derived =\n\t\tactive_reaction !== null && (active_reaction.f & DERIVED) !== 0\n\t\t\t? /** @type {Derived} */ (active_reaction)\n\t\t\t: null;\n\n\t/** @type {Derived<V>} */\n\tconst signal = {\n\t\tchildren: null,\n\t\tctx: component_context,\n\t\tdeps: null,\n\t\tequals,\n\t\tf: flags,\n\t\tfn,\n\t\treactions: null,\n\t\trv: 0,\n\t\tv: /** @type {V} */ (null),\n\t\twv: 0,\n\t\tparent: parent_derived ?? active_effect\n\t};\n\n\tif (DEV && tracing_mode_flag) {\n\t\tsignal.created = get_stack('CreatedAt');\n\t}\n\n\tif (parent_derived !== null) {\n\t\t(parent_derived.children ??= []).push(signal);\n\t}\n\n\treturn signal;\n}\n\n/**\n * @template V\n * @param {() => V} fn\n * @returns {Derived<V>}\n */\n/*#__NO_SIDE_EFFECTS__*/\nexport function derived_safe_equal(fn) {\n\tconst signal = derived(fn);\n\tsignal.equals = safe_equals;\n\treturn signal;\n}\n\n/**\n * @param {Derived} derived\n * @returns {void}\n */\nfunction destroy_derived_children(derived) {\n\tvar children = derived.children;\n\n\tif (children !== null) {\n\t\tderived.children = null;\n\n\t\tfor (var i = 0; i < children.length; i += 1) {\n\t\t\tvar child = children[i];\n\t\t\tif ((child.f & DERIVED) !== 0) {\n\t\t\t\tdestroy_derived(/** @type {Derived} */ (child));\n\t\t\t} else {\n\t\t\t\tdestroy_effect(/** @type {Effect} */ (child));\n\t\t\t}\n\t\t}\n\t}\n}\n\n/**\n * The currently updating deriveds, used to detect infinite recursion\n * in dev mode and provide a nicer error than 'too much recursion'\n * @type {Derived[]}\n */\nlet stack = [];\n\n/**\n * @param {Derived} derived\n * @returns {Effect | null}\n */\nfunction get_derived_parent_effect(derived) {\n\tvar parent = derived.parent;\n\twhile (parent !== null) {\n\t\tif ((parent.f & DERIVED) === 0) {\n\t\t\treturn /** @type {Effect} */ (parent);\n\t\t}\n\t\tparent = parent.parent;\n\t}\n\treturn null;\n}\n\n/**\n * @template T\n * @param {Derived} derived\n * @returns {T}\n */\nexport function execute_derived(derived) {\n\tvar value;\n\tvar prev_active_effect = active_effect;\n\n\tset_active_effect(get_derived_parent_effect(derived));\n\n\tif (DEV) {\n\t\tlet prev_inspect_effects = inspect_effects;\n\t\tset_inspect_effects(new Set());\n\t\ttry {\n\t\t\tif (stack.includes(derived)) {\n\t\t\t\te.derived_references_self();\n\t\t\t}\n\n\t\t\tstack.push(derived);\n\n\t\t\tdestroy_derived_children(derived);\n\t\t\tvalue = update_reaction(derived);\n\t\t} finally {\n\t\t\tset_active_effect(prev_active_effect);\n\t\t\tset_inspect_effects(prev_inspect_effects);\n\t\t\tstack.pop();\n\t\t}\n\t} else {\n\t\ttry {\n\t\t\tdestroy_derived_children(derived);\n\t\t\tvalue = update_reaction(derived);\n\t\t} finally {\n\t\t\tset_active_effect(prev_active_effect);\n\t\t}\n\t}\n\n\treturn value;\n}\n\n/**\n * @param {Derived} derived\n * @returns {void}\n */\nexport function update_derived(derived) {\n\tvar value = execute_derived(derived);\n\tvar status =\n\t\t(skip_reaction || (derived.f & UNOWNED) !== 0) && derived.deps !== null ? MAYBE_DIRTY : CLEAN;\n\n\tset_signal_status(derived, status);\n\n\tif (!derived.equals(value)) {\n\t\tderived.v = value;\n\t\tderived.wv = increment_write_version();\n\t}\n}\n\n/**\n * @param {Derived} derived\n * @returns {void}\n */\nexport function destroy_derived(derived) {\n\tdestroy_derived_children(derived);\n\tremove_reactions(derived, 0);\n\tset_signal_status(derived, DESTROYED);\n\n\tderived.v = derived.children = derived.deps = derived.ctx = derived.reactions = null;\n}\n", "/* This file is generated by scripts/process-messages/index.js. Do not edit! */\n\nimport { DEV } from 'esm-env';\n\n/**\n * Cannot use `{@render children(...)}` if the parent component uses `let:` directives. Consider using a named snippet instead\n * @returns {never}\n */\nexport function invalid_default_snippet() {\n\tif (DEV) {\n\t\tconst error = new Error(`invalid_default_snippet\\nCannot use \\`{@render children(...)}\\` if the parent component uses \\`let:\\` directives. Consider using a named snippet instead\\nhttps://svelte.dev/e/invalid_default_snippet`);\n\n\t\terror.name = 'Svelte error';\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/invalid_default_snippet`);\n\t}\n}\n\n/**\n * `%name%(...)` can only be used during component initialisation\n * @param {string} name\n * @returns {never}\n */\nexport function lifecycle_outside_component(name) {\n\tif (DEV) {\n\t\tconst error = new Error(`lifecycle_outside_component\\n\\`${name}(...)\\` can only be used during component initialisation\\nhttps://svelte.dev/e/lifecycle_outside_component`);\n\n\t\terror.name = 'Svelte error';\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/lifecycle_outside_component`);\n\t}\n}\n\n/**\n * `%name%` is not a store with a `subscribe` method\n * @param {string} name\n * @returns {never}\n */\nexport function store_invalid_shape(name) {\n\tif (DEV) {\n\t\tconst error = new Error(`store_invalid_shape\\n\\`${name}\\` is not a store with a \\`subscribe\\` method\\nhttps://svelte.dev/e/store_invalid_shape`);\n\n\t\terror.name = 'Svelte error';\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/store_invalid_shape`);\n\t}\n}\n\n/**\n * The `this` prop on `<svelte:element>` must be a string, if defined\n * @returns {never}\n */\nexport function svelte_element_invalid_this_value() {\n\tif (DEV) {\n\t\tconst error = new Error(`svelte_element_invalid_this_value\\nThe \\`this\\` prop on \\`<svelte:element>\\` must be a string, if defined\\nhttps://svelte.dev/e/svelte_element_invalid_this_value`);\n\n\t\terror.name = 'Svelte error';\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/svelte_element_invalid_this_value`);\n\t}\n}", "/** @import { ComponentContext, Derived, Effect, Reaction, Signal, Source, Value } from '#client' */\nimport { DEV } from 'esm-env';\nimport { define_property, get_descriptors, get_prototype_of, index_of } from '../shared/utils.js';\nimport {\n\tdestroy_block_effect_children,\n\tdestroy_effect_children,\n\tdestroy_effect_deriveds,\n\teffect,\n\texecute_effect_teardown,\n\tunlink_effect\n} from './reactivity/effects.js';\nimport {\n\tEFFECT,\n\tRENDER_EFFECT,\n\tDIRTY,\n\tMAYBE_DIRTY,\n\tCLEAN,\n\tDERIVED,\n\tUNOWNED,\n\tDESTROYED,\n\tINERT,\n\tBRANCH_EFFECT,\n\tSTATE_SYMBOL,\n\tBLOCK_EFFECT,\n\tROOT_EFFECT,\n\tLEGACY_DERIVED_PROP,\n\tDISCONNECTED,\n\tBOUNDARY_EFFECT\n} from './constants.js';\nimport { flush_tasks } from './dom/task.js';\nimport { add_owner } from './dev/ownership.js';\nimport { internal_set, set, source } from './reactivity/sources.js';\nimport { destroy_derived, execute_derived, update_derived } from './reactivity/deriveds.js';\nimport * as e from './errors.js';\nimport { lifecycle_outside_component } from '../shared/errors.js';\nimport { FILENAME } from '../../constants.js';\nimport { legacy_mode_flag, tracing_mode_flag } from '../flags/index.js';\nimport { tracing_expressions, get_stack } from './dev/tracing.js';\n\nconst FLUSH_MICROTASK = 0;\nconst FLUSH_SYNC = 1;\n// Used for DEV time error handling\n/** @param {WeakSet<Error>} value */\nconst handled_errors = new WeakSet();\nexport let is_throwing_error = false;\n\n// Used for controlling the flush of effects.\nlet scheduler_mode = FLUSH_MICROTASK;\n// Used for handling scheduling\nlet is_micro_task_queued = false;\n\n/** @type {Effect | null} */\nlet last_scheduled_effect = null;\n\nexport let is_flushing_effect = false;\nexport let is_destroying_effect = false;\n\n/** @param {boolean} value */\nexport function set_is_flushing_effect(value) {\n\tis_flushing_effect = value;\n}\n\n/** @param {boolean} value */\nexport function set_is_destroying_effect(value) {\n\tis_destroying_effect = value;\n}\n\n// Handle effect queues\n\n/** @type {Effect[]} */\nlet queued_root_effects = [];\n\nlet flush_count = 0;\n/** @type {Effect[]} Stack of effects, dev only */\nlet dev_effect_stack = [];\n// Handle signal reactivity tree dependencies and reactions\n\n/** @type {null | Reaction} */\nexport let active_reaction = null;\n\n/** @param {null | Reaction} reaction */\nexport function set_active_reaction(reaction) {\n\tactive_reaction = reaction;\n}\n\n/** @type {null | Effect} */\nexport let active_effect = null;\n\n/** @param {null | Effect} effect */\nexport function set_active_effect(effect) {\n\tactive_effect = effect;\n}\n\n/**\n * When sources are created within a derived, we record them so that we can safely allow\n * local mutations to these sources without the side-effect error being invoked unnecessarily.\n * @type {null | Source[]}\n */\nexport let derived_sources = null;\n\n/**\n * @param {Source[] | null} sources\n */\nexport function set_derived_sources(sources) {\n\tderived_sources = sources;\n}\n\n/**\n * The dependencies of the reaction that is currently being executed. In many cases,\n * the dependencies are unchanged between runs, and so this will be `null` unless\n * and until a new dependency is accessed — we track this via `skipped_deps`\n * @type {null | Value[]}\n */\nexport let new_deps = null;\n\nlet skipped_deps = 0;\n\n/**\n * Tracks writes that the effect it's executed in doesn't listen to yet,\n * so that the dependency can be added to the effect later on if it then reads it\n * @type {null | Source[]}\n */\nexport let untracked_writes = null;\n\n/** @param {null | Source[]} value */\nexport function set_untracked_writes(value) {\n\tuntracked_writes = value;\n}\n\n/**\n * @type {number} Used by sources and deriveds for handling updates.\n * Version starts from 1 so that unowned deriveds differentiate between a created effect and a run one for tracing\n **/\nlet write_version = 1;\n\n/** @type {number} Used to version each read of a source of derived to avoid duplicating depedencies inside a reaction */\nlet read_version = 0;\n\n// If we are working with a get() chain that has no active container,\n// to prevent memory leaks, we skip adding the reaction.\nexport let skip_reaction = false;\n// Handle collecting all signals which are read during a specific time frame\n/** @type {Set<Value> | null} */\nexport let captured_signals = null;\n\n/** @param {Set<Value> | null} value */\nexport function set_captured_signals(value) {\n\tcaptured_signals = value;\n}\n\n// Handling runtime component context\n/** @type {ComponentContext | null} */\nexport let component_context = null;\n\n/** @param {ComponentContext | null} context */\nexport function set_component_context(context) {\n\tcomponent_context = context;\n}\n\n/**\n * The current component function. Different from current component context:\n * ```html\n * <!-- App.svelte -->\n * <Foo>\n *   <Bar /> <!-- context == Foo.svelte, function == App.svelte -->\n * </Foo>\n * ```\n * @type {ComponentContext['function']}\n */\nexport let dev_current_component_function = null;\n\n/** @param {ComponentContext['function']} fn */\nexport function set_dev_current_component_function(fn) {\n\tdev_current_component_function = fn;\n}\n\nexport function increment_write_version() {\n\treturn ++write_version;\n}\n\n/** @returns {boolean} */\nexport function is_runes() {\n\treturn !legacy_mode_flag || (component_context !== null && component_context.l === null);\n}\n\n/**\n * Determines whether a derived or effect is dirty.\n * If it is MAYBE_DIRTY, will set the status to CLEAN\n * @param {Reaction} reaction\n * @returns {boolean}\n */\nexport function check_dirtiness(reaction) {\n\tvar flags = reaction.f;\n\n\tif ((flags & DIRTY) !== 0) {\n\t\treturn true;\n\t}\n\n\tif ((flags & MAYBE_DIRTY) !== 0) {\n\t\tvar dependencies = reaction.deps;\n\t\tvar is_unowned = (flags & UNOWNED) !== 0;\n\n\t\tif (dependencies !== null) {\n\t\t\tvar i;\n\t\t\tvar dependency;\n\t\t\tvar is_disconnected = (flags & DISCONNECTED) !== 0;\n\t\t\tvar is_unowned_connected = is_unowned && active_effect !== null && !skip_reaction;\n\t\t\tvar length = dependencies.length;\n\n\t\t\t// If we are working with a disconnected or an unowned signal that is now connected (due to an active effect)\n\t\t\t// then we need to re-connect the reaction to the dependency\n\t\t\tif (is_disconnected || is_unowned_connected) {\n\t\t\t\tfor (i = 0; i < length; i++) {\n\t\t\t\t\tdependency = dependencies[i];\n\n\t\t\t\t\t// We always re-add all reactions (even duplicates) if the derived was\n\t\t\t\t\t// previously disconnected\n\t\t\t\t\tif (is_disconnected || !dependency?.reactions?.includes(reaction)) {\n\t\t\t\t\t\t(dependency.reactions ??= []).push(reaction);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tif (is_disconnected) {\n\t\t\t\t\treaction.f ^= DISCONNECTED;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tfor (i = 0; i < length; i++) {\n\t\t\t\tdependency = dependencies[i];\n\n\t\t\t\tif (check_dirtiness(/** @type {Derived} */ (dependency))) {\n\t\t\t\t\tupdate_derived(/** @type {Derived} */ (dependency));\n\t\t\t\t}\n\n\t\t\t\tif (dependency.wv > reaction.wv) {\n\t\t\t\t\treturn true;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// Unowned signals should never be marked as clean unless they\n\t\t// are used within an active_effect without skip_reaction\n\t\tif (!is_unowned || (active_effect !== null && !skip_reaction)) {\n\t\t\tset_signal_status(reaction, CLEAN);\n\t\t}\n\t}\n\n\treturn false;\n}\n\n/**\n * @param {unknown} error\n * @param {Effect} effect\n */\nfunction propagate_error(error, effect) {\n\t/** @type {Effect | null} */\n\tvar current = effect;\n\n\twhile (current !== null) {\n\t\tif ((current.f & BOUNDARY_EFFECT) !== 0) {\n\t\t\ttry {\n\t\t\t\t// @ts-expect-error\n\t\t\t\tcurrent.fn(error);\n\t\t\t\treturn;\n\t\t\t} catch {\n\t\t\t\t// Remove boundary flag from effect\n\t\t\t\tcurrent.f ^= BOUNDARY_EFFECT;\n\t\t\t}\n\t\t}\n\n\t\tcurrent = current.parent;\n\t}\n\n\tis_throwing_error = false;\n\tthrow error;\n}\n\n/**\n * @param {Effect} effect\n */\nfunction should_rethrow_error(effect) {\n\treturn (\n\t\t(effect.f & DESTROYED) === 0 &&\n\t\t(effect.parent === null || (effect.parent.f & BOUNDARY_EFFECT) === 0)\n\t);\n}\n\nexport function reset_is_throwing_error() {\n\tis_throwing_error = false;\n}\n\n/**\n * @param {unknown} error\n * @param {Effect} effect\n * @param {Effect | null} previous_effect\n * @param {ComponentContext | null} component_context\n */\nexport function handle_error(error, effect, previous_effect, component_context) {\n\tif (is_throwing_error) {\n\t\tif (previous_effect === null) {\n\t\t\tis_throwing_error = false;\n\t\t}\n\n\t\tif (should_rethrow_error(effect)) {\n\t\t\tthrow error;\n\t\t}\n\n\t\treturn;\n\t}\n\n\tif (previous_effect !== null) {\n\t\tis_throwing_error = true;\n\t}\n\n\tif (\n\t\t!DEV ||\n\t\tcomponent_context === null ||\n\t\t!(error instanceof Error) ||\n\t\thandled_errors.has(error)\n\t) {\n\t\tpropagate_error(error, effect);\n\t\treturn;\n\t}\n\n\thandled_errors.add(error);\n\n\tconst component_stack = [];\n\n\tconst effect_name = effect.fn?.name;\n\n\tif (effect_name) {\n\t\tcomponent_stack.push(effect_name);\n\t}\n\n\t/** @type {ComponentContext | null} */\n\tlet current_context = component_context;\n\n\twhile (current_context !== null) {\n\t\tif (DEV) {\n\t\t\t/** @type {string} */\n\t\t\tvar filename = current_context.function?.[FILENAME];\n\n\t\t\tif (filename) {\n\t\t\t\tconst file = filename.split('/').pop();\n\t\t\t\tcomponent_stack.push(file);\n\t\t\t}\n\t\t}\n\n\t\tcurrent_context = current_context.p;\n\t}\n\n\tconst indent = /Firefox/.test(navigator.userAgent) ? '  ' : '\\t';\n\tdefine_property(error, 'message', {\n\t\tvalue: error.message + `\\n${component_stack.map((name) => `\\n${indent}in ${name}`).join('')}\\n`\n\t});\n\tdefine_property(error, 'component_stack', {\n\t\tvalue: component_stack\n\t});\n\n\tconst stack = error.stack;\n\n\t// Filter out internal files from callstack\n\tif (stack) {\n\t\tconst lines = stack.split('\\n');\n\t\tconst new_lines = [];\n\t\tfor (let i = 0; i < lines.length; i++) {\n\t\t\tconst line = lines[i];\n\t\t\tif (line.includes('svelte/src/internal')) {\n\t\t\t\tcontinue;\n\t\t\t}\n\t\t\tnew_lines.push(line);\n\t\t}\n\t\tdefine_property(error, 'stack', {\n\t\t\tvalue: new_lines.join('\\n')\n\t\t});\n\t}\n\n\tpropagate_error(error, effect);\n\n\tif (should_rethrow_error(effect)) {\n\t\tthrow error;\n\t}\n}\n\n/**\n * @param {Value} signal\n * @param {Effect} effect\n * @param {number} [depth]\n */\nfunction schedule_possible_effect_self_invalidation(signal, effect, depth = 0) {\n\tvar reactions = signal.reactions;\n\tif (reactions === null) return;\n\n\tfor (var i = 0; i < reactions.length; i++) {\n\t\tvar reaction = reactions[i];\n\t\tif ((reaction.f & DERIVED) !== 0) {\n\t\t\tschedule_possible_effect_self_invalidation(\n\t\t\t\t/** @type {Derived} */ (reaction),\n\t\t\t\teffect,\n\t\t\t\tdepth + 1\n\t\t\t);\n\t\t} else if (effect === reaction) {\n\t\t\tif (depth === 0) {\n\t\t\t\tset_signal_status(reaction, DIRTY);\n\t\t\t} else if ((reaction.f & CLEAN) !== 0) {\n\t\t\t\tset_signal_status(reaction, MAYBE_DIRTY);\n\t\t\t}\n\t\t\tschedule_effect(/** @type {Effect} */ (reaction));\n\t\t}\n\t}\n}\n\n/**\n * @template V\n * @param {Reaction} reaction\n * @returns {V}\n */\nexport function update_reaction(reaction) {\n\tvar previous_deps = new_deps;\n\tvar previous_skipped_deps = skipped_deps;\n\tvar previous_untracked_writes = untracked_writes;\n\tvar previous_reaction = active_reaction;\n\tvar previous_skip_reaction = skip_reaction;\n\tvar prev_derived_sources = derived_sources;\n\tvar previous_component_context = component_context;\n\tvar flags = reaction.f;\n\n\tnew_deps = /** @type {null | Value[]} */ (null);\n\tskipped_deps = 0;\n\tuntracked_writes = null;\n\tactive_reaction = (flags & (BRANCH_EFFECT | ROOT_EFFECT)) === 0 ? reaction : null;\n\tskip_reaction = !is_flushing_effect && (flags & UNOWNED) !== 0;\n\tderived_sources = null;\n\tcomponent_context = reaction.ctx;\n\tread_version++;\n\n\ttry {\n\t\tvar result = /** @type {Function} */ (0, reaction.fn)();\n\t\tvar deps = reaction.deps;\n\n\t\tif (new_deps !== null) {\n\t\t\tvar i;\n\n\t\t\tremove_reactions(reaction, skipped_deps);\n\n\t\t\tif (deps !== null && skipped_deps > 0) {\n\t\t\t\tdeps.length = skipped_deps + new_deps.length;\n\t\t\t\tfor (i = 0; i < new_deps.length; i++) {\n\t\t\t\t\tdeps[skipped_deps + i] = new_deps[i];\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\treaction.deps = deps = new_deps;\n\t\t\t}\n\n\t\t\tif (!skip_reaction) {\n\t\t\t\tfor (i = skipped_deps; i < deps.length; i++) {\n\t\t\t\t\t(deps[i].reactions ??= []).push(reaction);\n\t\t\t\t}\n\t\t\t}\n\t\t} else if (deps !== null && skipped_deps < deps.length) {\n\t\t\tremove_reactions(reaction, skipped_deps);\n\t\t\tdeps.length = skipped_deps;\n\t\t}\n\n\t\t// If we're inside an effect and we have untracked writes, then we need to\n\t\t// ensure that if any of those untracked writes result in re-invalidation\n\t\t// of the current effect, then that happens accordingly\n\t\tif (\n\t\t\tis_runes() &&\n\t\t\tuntracked_writes !== null &&\n\t\t\t(reaction.f & (DERIVED | MAYBE_DIRTY | DIRTY)) === 0\n\t\t) {\n\t\t\tfor (i = 0; i < /** @type {Source[]} */ (untracked_writes).length; i++) {\n\t\t\t\tschedule_possible_effect_self_invalidation(\n\t\t\t\t\tuntracked_writes[i],\n\t\t\t\t\t/** @type {Effect} */ (reaction)\n\t\t\t\t);\n\t\t\t}\n\t\t}\n\n\t\t// If we are returning to an previous reaction then\n\t\t// we need to increment the read version to ensure that\n\t\t// any dependencies in this reaction aren't marked with\n\t\t// the same version\n\t\tif (previous_reaction !== null) {\n\t\t\tread_version++;\n\t\t}\n\n\t\treturn result;\n\t} finally {\n\t\tnew_deps = previous_deps;\n\t\tskipped_deps = previous_skipped_deps;\n\t\tuntracked_writes = previous_untracked_writes;\n\t\tactive_reaction = previous_reaction;\n\t\tskip_reaction = previous_skip_reaction;\n\t\tderived_sources = prev_derived_sources;\n\t\tcomponent_context = previous_component_context;\n\t}\n}\n\n/**\n * @template V\n * @param {Reaction} signal\n * @param {Value<V>} dependency\n * @returns {void}\n */\nfunction remove_reaction(signal, dependency) {\n\tlet reactions = dependency.reactions;\n\tif (reactions !== null) {\n\t\tvar index = index_of.call(reactions, signal);\n\t\tif (index !== -1) {\n\t\t\tvar new_length = reactions.length - 1;\n\t\t\tif (new_length === 0) {\n\t\t\t\treactions = dependency.reactions = null;\n\t\t\t} else {\n\t\t\t\t// Swap with last element and then remove.\n\t\t\t\treactions[index] = reactions[new_length];\n\t\t\t\treactions.pop();\n\t\t\t}\n\t\t}\n\t}\n\t// If the derived has no reactions, then we can disconnect it from the graph,\n\t// allowing it to either reconnect in the future, or be GC'd by the VM.\n\tif (\n\t\treactions === null &&\n\t\t(dependency.f & DERIVED) !== 0 &&\n\t\t// Destroying a child effect while updating a parent effect can cause a dependency to appear\n\t\t// to be unused, when in fact it is used by the currently-updating parent. Checking `new_deps`\n\t\t// allows us to skip the expensive work of disconnecting and immediately reconnecting it\n\t\t(new_deps === null || !new_deps.includes(dependency))\n\t) {\n\t\tset_signal_status(dependency, MAYBE_DIRTY);\n\t\t// If we are working with a derived that is owned by an effect, then mark it as being\n\t\t// disconnected.\n\t\tif ((dependency.f & (UNOWNED | DISCONNECTED)) === 0) {\n\t\t\tdependency.f ^= DISCONNECTED;\n\t\t}\n\t\tremove_reactions(/** @type {Derived} **/ (dependency), 0);\n\t}\n}\n\n/**\n * @param {Reaction} signal\n * @param {number} start_index\n * @returns {void}\n */\nexport function remove_reactions(signal, start_index) {\n\tvar dependencies = signal.deps;\n\tif (dependencies === null) return;\n\n\tfor (var i = start_index; i < dependencies.length; i++) {\n\t\tremove_reaction(signal, dependencies[i]);\n\t}\n}\n\n/**\n * @param {Effect} effect\n * @returns {void}\n */\nexport function update_effect(effect) {\n\tvar flags = effect.f;\n\n\tif ((flags & DESTROYED) !== 0) {\n\t\treturn;\n\t}\n\n\tset_signal_status(effect, CLEAN);\n\n\tvar previous_effect = active_effect;\n\tvar previous_component_context = component_context;\n\n\tactive_effect = effect;\n\n\tif (DEV) {\n\t\tvar previous_component_fn = dev_current_component_function;\n\t\tdev_current_component_function = effect.component_function;\n\t}\n\n\ttry {\n\t\tif ((flags & BLOCK_EFFECT) !== 0) {\n\t\t\tdestroy_block_effect_children(effect);\n\t\t} else {\n\t\t\tdestroy_effect_children(effect);\n\t\t}\n\t\tdestroy_effect_deriveds(effect);\n\n\t\texecute_effect_teardown(effect);\n\t\tvar teardown = update_reaction(effect);\n\t\teffect.teardown = typeof teardown === 'function' ? teardown : null;\n\t\teffect.wv = write_version;\n\n\t\tvar deps = effect.deps;\n\n\t\t// In DEV, we need to handle a case where $inspect.trace() might\n\t\t// incorrectly state a source dependency has not changed when it has.\n\t\t// That's beacuse that source was changed by the same effect, causing\n\t\t// the versions to match. We can avoid this by incrementing the version\n\t\tif (DEV && tracing_mode_flag && (effect.f & DIRTY) !== 0 && deps !== null) {\n\t\t\tfor (let i = 0; i < deps.length; i++) {\n\t\t\t\tvar dep = deps[i];\n\t\t\t\tif (dep.trace_need_increase) {\n\t\t\t\t\tdep.wv = increment_write_version();\n\t\t\t\t\tdep.trace_need_increase = undefined;\n\t\t\t\t\tdep.trace_v = undefined;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tif (DEV) {\n\t\t\tdev_effect_stack.push(effect);\n\t\t}\n\t} catch (error) {\n\t\thandle_error(error, effect, previous_effect, previous_component_context || effect.ctx);\n\t} finally {\n\t\tactive_effect = previous_effect;\n\n\t\tif (DEV) {\n\t\t\tdev_current_component_function = previous_component_fn;\n\t\t}\n\t}\n}\n\nfunction log_effect_stack() {\n\t// eslint-disable-next-line no-console\n\tconsole.error(\n\t\t'Last ten effects were: ',\n\t\tdev_effect_stack.slice(-10).map((d) => d.fn)\n\t);\n\tdev_effect_stack = [];\n}\n\nfunction infinite_loop_guard() {\n\tif (flush_count > 1000) {\n\t\tflush_count = 0;\n\t\ttry {\n\t\t\te.effect_update_depth_exceeded();\n\t\t} catch (error) {\n\t\t\tif (DEV) {\n\t\t\t\t// stack is garbage, ignore. Instead add a console.error message.\n\t\t\t\tdefine_property(error, 'stack', {\n\t\t\t\t\tvalue: ''\n\t\t\t\t});\n\t\t\t}\n\t\t\t// Try and handle the error so it can be caught at a boundary, that's\n\t\t\t// if there's an effect available from when it was last scheduled\n\t\t\tif (last_scheduled_effect !== null) {\n\t\t\t\tif (DEV) {\n\t\t\t\t\ttry {\n\t\t\t\t\t\thandle_error(error, last_scheduled_effect, null, null);\n\t\t\t\t\t} catch (e) {\n\t\t\t\t\t\t// Only log the effect stack if the error is re-thrown\n\t\t\t\t\t\tlog_effect_stack();\n\t\t\t\t\t\tthrow e;\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\thandle_error(error, last_scheduled_effect, null, null);\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tif (DEV) {\n\t\t\t\t\tlog_effect_stack();\n\t\t\t\t}\n\t\t\t\tthrow error;\n\t\t\t}\n\t\t}\n\t}\n\tflush_count++;\n}\n\n/**\n * @param {Array<Effect>} root_effects\n * @returns {void}\n */\nfunction flush_queued_root_effects(root_effects) {\n\tvar length = root_effects.length;\n\tif (length === 0) {\n\t\treturn;\n\t}\n\tinfinite_loop_guard();\n\n\tvar previously_flushing_effect = is_flushing_effect;\n\tis_flushing_effect = true;\n\n\ttry {\n\t\tfor (var i = 0; i < length; i++) {\n\t\t\tvar effect = root_effects[i];\n\n\t\t\tif ((effect.f & CLEAN) === 0) {\n\t\t\t\teffect.f ^= CLEAN;\n\t\t\t}\n\n\t\t\t/** @type {Effect[]} */\n\t\t\tvar collected_effects = [];\n\n\t\t\tprocess_effects(effect, collected_effects);\n\t\t\tflush_queued_effects(collected_effects);\n\t\t}\n\t} finally {\n\t\tis_flushing_effect = previously_flushing_effect;\n\t}\n}\n\n/**\n * @param {Array<Effect>} effects\n * @returns {void}\n */\nfunction flush_queued_effects(effects) {\n\tvar length = effects.length;\n\tif (length === 0) return;\n\n\tfor (var i = 0; i < length; i++) {\n\t\tvar effect = effects[i];\n\n\t\tif ((effect.f & (DESTROYED | INERT)) === 0) {\n\t\t\ttry {\n\t\t\t\tif (check_dirtiness(effect)) {\n\t\t\t\t\tupdate_effect(effect);\n\n\t\t\t\t\t// Effects with no dependencies or teardown do not get added to the effect tree.\n\t\t\t\t\t// Deferred effects (e.g. `$effect(...)`) _are_ added to the tree because we\n\t\t\t\t\t// don't know if we need to keep them until they are executed. Doing the check\n\t\t\t\t\t// here (rather than in `update_effect`) allows us to skip the work for\n\t\t\t\t\t// immediate effects.\n\t\t\t\t\tif (effect.deps === null && effect.first === null && effect.nodes_start === null) {\n\t\t\t\t\t\tif (effect.teardown === null) {\n\t\t\t\t\t\t\t// remove this effect from the graph\n\t\t\t\t\t\t\tunlink_effect(effect);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t// keep the effect in the graph, but free up some memory\n\t\t\t\t\t\t\teffect.fn = null;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\thandle_error(error, effect, null, effect.ctx);\n\t\t\t}\n\t\t}\n\t}\n}\n\nfunction process_deferred() {\n\tis_micro_task_queued = false;\n\tif (flush_count > 1001) {\n\t\treturn;\n\t}\n\tconst previous_queued_root_effects = queued_root_effects;\n\tqueued_root_effects = [];\n\tflush_queued_root_effects(previous_queued_root_effects);\n\n\tif (!is_micro_task_queued) {\n\t\tflush_count = 0;\n\t\tlast_scheduled_effect = null;\n\t\tif (DEV) {\n\t\t\tdev_effect_stack = [];\n\t\t}\n\t}\n}\n\n/**\n * @param {Effect} signal\n * @returns {void}\n */\nexport function schedule_effect(signal) {\n\tif (scheduler_mode === FLUSH_MICROTASK) {\n\t\tif (!is_micro_task_queued) {\n\t\t\tis_micro_task_queued = true;\n\t\t\tqueueMicrotask(process_deferred);\n\t\t}\n\t}\n\n\tlast_scheduled_effect = signal;\n\n\tvar effect = signal;\n\n\twhile (effect.parent !== null) {\n\t\teffect = effect.parent;\n\t\tvar flags = effect.f;\n\n\t\tif ((flags & (ROOT_EFFECT | BRANCH_EFFECT)) !== 0) {\n\t\t\tif ((flags & CLEAN) === 0) return;\n\t\t\teffect.f ^= CLEAN;\n\t\t}\n\t}\n\n\tqueued_root_effects.push(effect);\n}\n\n/**\n *\n * This function both runs render effects and collects user effects in topological order\n * from the starting effect passed in. Effects will be collected when they match the filtered\n * bitwise flag passed in only. The collected effects array will be populated with all the user\n * effects to be flushed.\n *\n * @param {Effect} effect\n * @param {Effect[]} collected_effects\n * @returns {void}\n */\nfunction process_effects(effect, collected_effects) {\n\tvar current_effect = effect.first;\n\tvar effects = [];\n\n\tmain_loop: while (current_effect !== null) {\n\t\tvar flags = current_effect.f;\n\t\tvar is_branch = (flags & BRANCH_EFFECT) !== 0;\n\t\tvar is_skippable_branch = is_branch && (flags & CLEAN) !== 0;\n\t\tvar sibling = current_effect.next;\n\n\t\tif (!is_skippable_branch && (flags & INERT) === 0) {\n\t\t\tif ((flags & RENDER_EFFECT) !== 0) {\n\t\t\t\tif (is_branch) {\n\t\t\t\t\tcurrent_effect.f ^= CLEAN;\n\t\t\t\t} else {\n\t\t\t\t\ttry {\n\t\t\t\t\t\tif (check_dirtiness(current_effect)) {\n\t\t\t\t\t\t\tupdate_effect(current_effect);\n\t\t\t\t\t\t}\n\t\t\t\t\t} catch (error) {\n\t\t\t\t\t\thandle_error(error, current_effect, null, current_effect.ctx);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tvar child = current_effect.first;\n\n\t\t\t\tif (child !== null) {\n\t\t\t\t\tcurrent_effect = child;\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t} else if ((flags & EFFECT) !== 0) {\n\t\t\t\teffects.push(current_effect);\n\t\t\t}\n\t\t}\n\n\t\tif (sibling === null) {\n\t\t\tlet parent = current_effect.parent;\n\n\t\t\twhile (parent !== null) {\n\t\t\t\tif (effect === parent) {\n\t\t\t\t\tbreak main_loop;\n\t\t\t\t}\n\t\t\t\tvar parent_sibling = parent.next;\n\t\t\t\tif (parent_sibling !== null) {\n\t\t\t\t\tcurrent_effect = parent_sibling;\n\t\t\t\t\tcontinue main_loop;\n\t\t\t\t}\n\t\t\t\tparent = parent.parent;\n\t\t\t}\n\t\t}\n\n\t\tcurrent_effect = sibling;\n\t}\n\n\t// We might be dealing with many effects here, far more than can be spread into\n\t// an array push call (callstack overflow). So let's deal with each effect in a loop.\n\tfor (var i = 0; i < effects.length; i++) {\n\t\tchild = effects[i];\n\t\tcollected_effects.push(child);\n\t\tprocess_effects(child, collected_effects);\n\t}\n}\n\n/**\n * Internal version of `flushSync` with the option to not flush previous effects.\n * Returns the result of the passed function, if given.\n * @param {() => any} [fn]\n * @returns {any}\n */\nexport function flush_sync(fn) {\n\tvar previous_scheduler_mode = scheduler_mode;\n\tvar previous_queued_root_effects = queued_root_effects;\n\n\ttry {\n\t\tinfinite_loop_guard();\n\n\t\t/** @type {Effect[]} */\n\t\tconst root_effects = [];\n\n\t\tscheduler_mode = FLUSH_SYNC;\n\t\tqueued_root_effects = root_effects;\n\t\tis_micro_task_queued = false;\n\n\t\tflush_queued_root_effects(previous_queued_root_effects);\n\n\t\tvar result = fn?.();\n\n\t\tflush_tasks();\n\t\tif (queued_root_effects.length > 0 || root_effects.length > 0) {\n\t\t\tflush_sync();\n\t\t}\n\n\t\tflush_count = 0;\n\t\tlast_scheduled_effect = null;\n\t\tif (DEV) {\n\t\t\tdev_effect_stack = [];\n\t\t}\n\n\t\treturn result;\n\t} finally {\n\t\tscheduler_mode = previous_scheduler_mode;\n\t\tqueued_root_effects = previous_queued_root_effects;\n\t}\n}\n\n/**\n * Returns a promise that resolves once any pending state changes have been applied.\n * @returns {Promise<void>}\n */\nexport async function tick() {\n\tawait Promise.resolve();\n\t// By calling flush_sync we guarantee that any pending state changes are applied after one tick.\n\t// TODO look into whether we can make flushing subsequent updates synchronously in the future.\n\tflush_sync();\n}\n\n/**\n * @template V\n * @param {Value<V>} signal\n * @returns {V}\n */\nexport function get(signal) {\n\tvar flags = signal.f;\n\tvar is_derived = (flags & DERIVED) !== 0;\n\n\t// If the derived is destroyed, just execute it again without retaining\n\t// its memoisation properties as the derived is stale\n\tif (is_derived && (flags & DESTROYED) !== 0) {\n\t\tvar value = execute_derived(/** @type {Derived} */ (signal));\n\t\t// Ensure the derived remains destroyed\n\t\tdestroy_derived(/** @type {Derived} */ (signal));\n\t\treturn value;\n\t}\n\n\tif (captured_signals !== null) {\n\t\tcaptured_signals.add(signal);\n\t}\n\n\t// Register the dependency on the current reaction signal.\n\tif (active_reaction !== null) {\n\t\tif (derived_sources !== null && derived_sources.includes(signal)) {\n\t\t\te.state_unsafe_local_read();\n\t\t}\n\t\tvar deps = active_reaction.deps;\n\t\tif (signal.rv < read_version) {\n\t\t\tsignal.rv = read_version;\n\t\t\t// If the signal is accessing the same dependencies in the same\n\t\t\t// order as it did last time, increment `skipped_deps`\n\t\t\t// rather than updating `new_deps`, which creates GC cost\n\t\t\tif (new_deps === null && deps !== null && deps[skipped_deps] === signal) {\n\t\t\t\tskipped_deps++;\n\t\t\t} else if (new_deps === null) {\n\t\t\t\tnew_deps = [signal];\n\t\t\t} else {\n\t\t\t\tnew_deps.push(signal);\n\t\t\t}\n\t\t}\n\t} else if (is_derived && /** @type {Derived} */ (signal).deps === null) {\n\t\tvar derived = /** @type {Derived} */ (signal);\n\t\tvar parent = derived.parent;\n\t\tvar target = derived;\n\n\t\twhile (parent !== null) {\n\t\t\t// Attach the derived to the nearest parent effect, if there are deriveds\n\t\t\t// in between then we also need to attach them too\n\t\t\tif ((parent.f & DERIVED) !== 0) {\n\t\t\t\tvar parent_derived = /** @type {Derived} */ (parent);\n\n\t\t\t\ttarget = parent_derived;\n\t\t\t\tparent = parent_derived.parent;\n\t\t\t} else {\n\t\t\t\tvar parent_effect = /** @type {Effect} */ (parent);\n\n\t\t\t\tif (!parent_effect.deriveds?.includes(target)) {\n\t\t\t\t\t(parent_effect.deriveds ??= []).push(target);\n\t\t\t\t}\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\t}\n\n\tif (is_derived) {\n\t\tderived = /** @type {Derived} */ (signal);\n\n\t\tif (check_dirtiness(derived)) {\n\t\t\tupdate_derived(derived);\n\t\t}\n\t}\n\n\tif (\n\t\tDEV &&\n\t\ttracing_mode_flag &&\n\t\ttracing_expressions !== null &&\n\t\tactive_reaction !== null &&\n\t\ttracing_expressions.reaction === active_reaction\n\t) {\n\t\t// Used when mapping state between special blocks like `each`\n\t\tif (signal.debug) {\n\t\t\tsignal.debug();\n\t\t} else if (signal.created) {\n\t\t\tvar entry = tracing_expressions.entries.get(signal);\n\n\t\t\tif (entry === undefined) {\n\t\t\t\tentry = { read: [] };\n\t\t\t\ttracing_expressions.entries.set(signal, entry);\n\t\t\t}\n\n\t\t\tentry.read.push(get_stack('TracedAt'));\n\t\t}\n\t}\n\n\treturn signal.v;\n}\n\n/**\n * Like `get`, but checks for `undefined`. Used for `var` declarations because they can be accessed before being declared\n * @template V\n * @param {Value<V> | undefined} signal\n * @returns {V | undefined}\n */\nexport function safe_get(signal) {\n\treturn signal && get(signal);\n}\n\n/**\n * Capture an array of all the signals that are read when `fn` is called\n * @template T\n * @param {() => T} fn\n */\nexport function capture_signals(fn) {\n\tvar previous_captured_signals = captured_signals;\n\tcaptured_signals = new Set();\n\n\tvar captured = captured_signals;\n\tvar signal;\n\n\ttry {\n\t\tuntrack(fn);\n\t\tif (previous_captured_signals !== null) {\n\t\t\tfor (signal of captured_signals) {\n\t\t\t\tprevious_captured_signals.add(signal);\n\t\t\t}\n\t\t}\n\t} finally {\n\t\tcaptured_signals = previous_captured_signals;\n\t}\n\n\treturn captured;\n}\n\n/**\n * Invokes a function and captures all signals that are read during the invocation,\n * then invalidates them.\n * @param {() => any} fn\n */\nexport function invalidate_inner_signals(fn) {\n\tvar captured = capture_signals(() => untrack(fn));\n\n\tfor (var signal of captured) {\n\t\t// Go one level up because derived signals created as part of props in legacy mode\n\t\tif ((signal.f & LEGACY_DERIVED_PROP) !== 0) {\n\t\t\tfor (const dep of /** @type {Derived} */ (signal).deps || []) {\n\t\t\t\tif ((dep.f & DERIVED) === 0) {\n\t\t\t\t\t// Use internal_set instead of set here and below to avoid mutation validation\n\t\t\t\t\tinternal_set(dep, dep.v);\n\t\t\t\t}\n\t\t\t}\n\t\t} else {\n\t\t\tinternal_set(signal, signal.v);\n\t\t}\n\t}\n}\n\n/**\n * When used inside a [`$derived`](https://svelte.dev/docs/svelte/$derived) or [`$effect`](https://svelte.dev/docs/svelte/$effect),\n * any state read inside `fn` will not be treated as a dependency.\n *\n * ```ts\n * $effect(() => {\n *   // this will run when `data` changes, but not when `time` changes\n *   save(data, {\n *     timestamp: untrack(() => time)\n *   });\n * });\n * ```\n * @template T\n * @param {() => T} fn\n * @returns {T}\n */\nexport function untrack(fn) {\n\tconst previous_reaction = active_reaction;\n\ttry {\n\t\tactive_reaction = null;\n\t\treturn fn();\n\t} finally {\n\t\tactive_reaction = previous_reaction;\n\t}\n}\n\nconst STATUS_MASK = ~(DIRTY | MAYBE_DIRTY | CLEAN);\n\n/**\n * @param {Signal} signal\n * @param {number} status\n * @returns {void}\n */\nexport function set_signal_status(signal, status) {\n\tsignal.f = (signal.f & STATUS_MASK) | status;\n}\n\n/**\n * Retrieves the context that belongs to the closest parent component with the specified `key`.\n * Must be called during component initialisation.\n *\n * @template T\n * @param {any} key\n * @returns {T}\n */\nexport function getContext(key) {\n\tconst context_map = get_or_init_context_map('getContext');\n\tconst result = /** @type {T} */ (context_map.get(key));\n\n\tif (DEV) {\n\t\tconst fn = /** @type {ComponentContext} */ (component_context).function;\n\t\tif (fn) {\n\t\t\tadd_owner(result, fn, true);\n\t\t}\n\t}\n\n\treturn result;\n}\n\n/**\n * Associates an arbitrary `context` object with the current component and the specified `key`\n * and returns that object. The context is then available to children of the component\n * (including slotted content) with `getContext`.\n *\n * Like lifecycle functions, this must be called during component initialisation.\n *\n * @template T\n * @param {any} key\n * @param {T} context\n * @returns {T}\n */\nexport function setContext(key, context) {\n\tconst context_map = get_or_init_context_map('setContext');\n\tcontext_map.set(key, context);\n\treturn context;\n}\n\n/**\n * Checks whether a given `key` has been set in the context of a parent component.\n * Must be called during component initialisation.\n *\n * @param {any} key\n * @returns {boolean}\n */\nexport function hasContext(key) {\n\tconst context_map = get_or_init_context_map('hasContext');\n\treturn context_map.has(key);\n}\n\n/**\n * Retrieves the whole context map that belongs to the closest parent component.\n * Must be called during component initialisation. Useful, for example, if you\n * programmatically create a component and want to pass the existing context to it.\n *\n * @template {Map<any, any>} [T=Map<any, any>]\n * @returns {T}\n */\nexport function getAllContexts() {\n\tconst context_map = get_or_init_context_map('getAllContexts');\n\n\tif (DEV) {\n\t\tconst fn = component_context?.function;\n\t\tif (fn) {\n\t\t\tfor (const value of context_map.values()) {\n\t\t\t\tadd_owner(value, fn, true);\n\t\t\t}\n\t\t}\n\t}\n\n\treturn /** @type {T} */ (context_map);\n}\n\n/**\n * @param {string} name\n * @returns {Map<unknown, unknown>}\n */\nfunction get_or_init_context_map(name) {\n\tif (component_context === null) {\n\t\tlifecycle_outside_component(name);\n\t}\n\n\treturn (component_context.c ??= new Map(get_parent_context(component_context) || undefined));\n}\n\n/**\n * @param {ComponentContext} component_context\n * @returns {Map<unknown, unknown> | null}\n */\nfunction get_parent_context(component_context) {\n\tlet parent = component_context.p;\n\twhile (parent !== null) {\n\t\tconst context_map = parent.c;\n\t\tif (context_map !== null) {\n\t\t\treturn context_map;\n\t\t}\n\t\tparent = parent.p;\n\t}\n\treturn null;\n}\n\n/**\n * @template {number | bigint} T\n * @param {Value<T>} signal\n * @param {1 | -1} [d]\n * @returns {T}\n */\nexport function update(signal, d = 1) {\n\tvar value = get(signal);\n\tvar result = d === 1 ? value++ : value--;\n\n\tset(signal, value);\n\n\t// @ts-expect-error\n\treturn result;\n}\n\n/**\n * @template {number | bigint} T\n * @param {Value<T>} signal\n * @param {1 | -1} [d]\n * @returns {T}\n */\nexport function update_pre(signal, d = 1) {\n\tvar value = get(signal);\n\n\t// @ts-expect-error\n\treturn set(signal, d === 1 ? ++value : --value);\n}\n\n/**\n * @param {Record<string, unknown>} obj\n * @param {string[]} keys\n * @returns {Record<string, unknown>}\n */\nexport function exclude_from_object(obj, keys) {\n\t/** @type {Record<string, unknown>} */\n\tvar result = {};\n\n\tfor (var key in obj) {\n\t\tif (!keys.includes(key)) {\n\t\t\tresult[key] = obj[key];\n\t\t}\n\t}\n\n\treturn result;\n}\n\n/**\n * @param {Record<string, unknown>} props\n * @param {any} runes\n * @param {Function} [fn]\n * @returns {void}\n */\nexport function push(props, runes = false, fn) {\n\tcomponent_context = {\n\t\tp: component_context,\n\t\tc: null,\n\t\te: null,\n\t\tm: false,\n\t\ts: props,\n\t\tx: null,\n\t\tl: null\n\t};\n\n\tif (legacy_mode_flag && !runes) {\n\t\tcomponent_context.l = {\n\t\t\ts: null,\n\t\t\tu: null,\n\t\t\tr1: [],\n\t\t\tr2: source(false)\n\t\t};\n\t}\n\n\tif (DEV) {\n\t\t// component function\n\t\tcomponent_context.function = fn;\n\t\tdev_current_component_function = fn;\n\t}\n}\n\n/**\n * @template {Record<string, any>} T\n * @param {T} [component]\n * @returns {T}\n */\nexport function pop(component) {\n\tconst context_stack_item = component_context;\n\tif (context_stack_item !== null) {\n\t\tif (component !== undefined) {\n\t\t\tcontext_stack_item.x = component;\n\t\t}\n\t\tconst component_effects = context_stack_item.e;\n\t\tif (component_effects !== null) {\n\t\t\tvar previous_effect = active_effect;\n\t\t\tvar previous_reaction = active_reaction;\n\t\t\tcontext_stack_item.e = null;\n\t\t\ttry {\n\t\t\t\tfor (var i = 0; i < component_effects.length; i++) {\n\t\t\t\t\tvar component_effect = component_effects[i];\n\t\t\t\t\tset_active_effect(component_effect.effect);\n\t\t\t\t\tset_active_reaction(component_effect.reaction);\n\t\t\t\t\teffect(component_effect.fn);\n\t\t\t\t}\n\t\t\t} finally {\n\t\t\t\tset_active_effect(previous_effect);\n\t\t\t\tset_active_reaction(previous_reaction);\n\t\t\t}\n\t\t}\n\t\tcomponent_context = context_stack_item.p;\n\t\tif (DEV) {\n\t\t\tdev_current_component_function = context_stack_item.p?.function ?? null;\n\t\t}\n\t\tcontext_stack_item.m = true;\n\t}\n\t// Micro-optimization: Don't set .a above to the empty object\n\t// so it can be garbage-collected when the return here is unused\n\treturn component || /** @type {T} */ ({});\n}\n\n/**\n * Possibly traverse an object and read all its properties so that they're all reactive in case this is `$state`.\n * Does only check first level of an object for performance reasons (heuristic should be good for 99% of all cases).\n * @param {any} value\n * @returns {void}\n */\nexport function deep_read_state(value) {\n\tif (typeof value !== 'object' || !value || value instanceof EventTarget) {\n\t\treturn;\n\t}\n\n\tif (STATE_SYMBOL in value) {\n\t\tdeep_read(value);\n\t} else if (!Array.isArray(value)) {\n\t\tfor (let key in value) {\n\t\t\tconst prop = value[key];\n\t\t\tif (typeof prop === 'object' && prop && STATE_SYMBOL in prop) {\n\t\t\t\tdeep_read(prop);\n\t\t\t}\n\t\t}\n\t}\n}\n\n/**\n * Deeply traverse an object and read all its properties\n * so that they're all reactive in case this is `$state`\n * @param {any} value\n * @param {Set<any>} visited\n * @returns {void}\n */\nexport function deep_read(value, visited = new Set()) {\n\tif (\n\t\ttypeof value === 'object' &&\n\t\tvalue !== null &&\n\t\t// We don't want to traverse DOM elements\n\t\t!(value instanceof EventTarget) &&\n\t\t!visited.has(value)\n\t) {\n\t\tvisited.add(value);\n\t\t// When working with a possible SvelteDate, this\n\t\t// will ensure we capture changes to it.\n\t\tif (value instanceof Date) {\n\t\t\tvalue.getTime();\n\t\t}\n\t\tfor (let key in value) {\n\t\t\ttry {\n\t\t\t\tdeep_read(value[key], visited);\n\t\t\t} catch (e) {\n\t\t\t\t// continue\n\t\t\t}\n\t\t}\n\t\tconst proto = get_prototype_of(value);\n\t\tif (\n\t\t\tproto !== Object.prototype &&\n\t\t\tproto !== Array.prototype &&\n\t\t\tproto !== Map.prototype &&\n\t\t\tproto !== Set.prototype &&\n\t\t\tproto !== Date.prototype\n\t\t) {\n\t\t\tconst descriptors = get_descriptors(proto);\n\t\t\tfor (let key in descriptors) {\n\t\t\t\tconst get = descriptors[key].get;\n\t\t\t\tif (get) {\n\t\t\t\t\ttry {\n\t\t\t\t\t\tget.call(value);\n\t\t\t\t\t} catch (e) {\n\t\t\t\t\t\t// continue\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n\nif (DEV) {\n\t/**\n\t * @param {string} rune\n\t */\n\tfunction throw_rune_error(rune) {\n\t\tif (!(rune in globalThis)) {\n\t\t\t// TODO if people start adjusting the \"this can contain runes\" config through v-p-s more, adjust this message\n\t\t\t/** @type {any} */\n\t\t\tlet value; // let's hope noone modifies this global, but belts and braces\n\t\t\tObject.defineProperty(globalThis, rune, {\n\t\t\t\tconfigurable: true,\n\t\t\t\t// eslint-disable-next-line getter-return\n\t\t\t\tget: () => {\n\t\t\t\t\tif (value !== undefined) {\n\t\t\t\t\t\treturn value;\n\t\t\t\t\t}\n\n\t\t\t\t\te.rune_outside_svelte(rune);\n\t\t\t\t},\n\t\t\t\tset: (v) => {\n\t\t\t\t\tvalue = v;\n\t\t\t\t}\n\t\t\t});\n\t\t}\n\t}\n\n\tthrow_rune_error('$state');\n\tthrow_rune_error('$effect');\n\tthrow_rune_error('$derived');\n\tthrow_rune_error('$inspect');\n\tthrow_rune_error('$props');\n\tthrow_rune_error('$bindable');\n}\n", "/** @import { TemplateNode } from '#client' */\n\nimport {\n\tHY<PERSON><PERSON>ION_END,\n\tHYDRATION_ERROR,\n\tHY<PERSON>ATION_START,\n\tHYDRATION_START_ELSE\n} from '../../../constants.js';\nimport * as w from '../warnings.js';\nimport { get_next_sibling } from './operations.js';\n\n/**\n * Use this variable to guard everything related to hydration code so it can be treeshaken out\n * if the user doesn't use the `hydrate` method and these code paths are therefore not needed.\n */\nexport let hydrating = false;\n\n/** @param {boolean} value */\nexport function set_hydrating(value) {\n\thydrating = value;\n}\n\n/**\n * The node that is currently being hydrated. This starts out as the first node inside the opening\n * <!--[--> comment, and updates each time a component calls `$.child(...)` or `$.sibling(...)`.\n * When entering a block (e.g. `{#if ...}`), `hydrate_node` is the block opening comment; by the\n * time we leave the block it is the closing comment, which serves as the block's anchor.\n * @type {TemplateNode}\n */\nexport let hydrate_node;\n\n/** @param {TemplateNode} node */\nexport function set_hydrate_node(node) {\n\tif (node === null) {\n\t\tw.hydration_mismatch();\n\t\tthrow HYDRATION_ERROR;\n\t}\n\n\treturn (hydrate_node = node);\n}\n\nexport function hydrate_next() {\n\treturn set_hydrate_node(/** @type {TemplateNode} */ (get_next_sibling(hydrate_node)));\n}\n\n/** @param {TemplateNode} node */\nexport function reset(node) {\n\tif (!hydrating) return;\n\n\t// If the node has remaining siblings, something has gone wrong\n\tif (get_next_sibling(hydrate_node) !== null) {\n\t\tw.hydration_mismatch();\n\t\tthrow HYDRATION_ERROR;\n\t}\n\n\thydrate_node = node;\n}\n\n/**\n * @param {HTMLTemplateElement} template\n */\nexport function hydrate_template(template) {\n\tif (hydrating) {\n\t\t// @ts-expect-error TemplateNode doesn't include DocumentFragment, but it's actually fine\n\t\thydrate_node = template.content;\n\t}\n}\n\nexport function next(count = 1) {\n\tif (hydrating) {\n\t\tvar i = count;\n\t\tvar node = hydrate_node;\n\n\t\twhile (i--) {\n\t\t\tnode = /** @type {TemplateNode} */ (get_next_sibling(node));\n\t\t}\n\n\t\thydrate_node = node;\n\t}\n}\n\n/**\n * Removes all nodes starting at `hydrate_node` up until the next hydration end comment\n */\nexport function remove_nodes() {\n\tvar depth = 0;\n\tvar node = hydrate_node;\n\n\twhile (true) {\n\t\tif (node.nodeType === 8) {\n\t\t\tvar data = /** @type {Comment} */ (node).data;\n\n\t\t\tif (data === HYDRATION_END) {\n\t\t\t\tif (depth === 0) return node;\n\t\t\t\tdepth -= 1;\n\t\t\t} else if (data === HYDRATION_START || data === HYDRATION_START_ELSE) {\n\t\t\t\tdepth += 1;\n\t\t\t}\n\t\t}\n\n\t\tvar next = /** @type {TemplateNode} */ (get_next_sibling(node));\n\t\tnode.remove();\n\t\tnode = next;\n\t}\n}\n", "/** @import { ProxyMetadata, ProxyStateObject, Source } from '#client' */\nimport { DEV } from 'esm-env';\nimport { get, component_context, active_effect } from './runtime.js';\nimport {\n\tarray_prototype,\n\tget_descriptor,\n\tget_prototype_of,\n\tis_array,\n\tobject_prototype\n} from '../shared/utils.js';\nimport { check_ownership, widen_ownership } from './dev/ownership.js';\nimport { source, set } from './reactivity/sources.js';\nimport { STATE_SYMBOL, STATE_SYMBOL_METADATA } from './constants.js';\nimport { UNINITIALIZED } from '../../constants.js';\nimport * as e from './errors.js';\nimport { get_stack } from './dev/tracing.js';\nimport { tracing_mode_flag } from '../flags/index.js';\n\n/**\n * @template T\n * @param {T} value\n * @param {ProxyMetadata | null} [parent]\n * @param {Source<T>} [prev] dev mode only\n * @returns {T}\n */\nexport function proxy(value, parent = null, prev) {\n\t/** @type {Error | null} */\n\tvar stack = null;\n\tif (DEV && tracing_mode_flag) {\n\t\tstack = get_stack('CreatedAt');\n\t}\n\t// if non-proxyable, or is already a proxy, return `value`\n\tif (typeof value !== 'object' || value === null || STATE_SYMBOL in value) {\n\t\treturn value;\n\t}\n\n\tconst prototype = get_prototype_of(value);\n\n\tif (prototype !== object_prototype && prototype !== array_prototype) {\n\t\treturn value;\n\t}\n\n\t/** @type {Map<any, Source<any>>} */\n\tvar sources = new Map();\n\tvar is_proxied_array = is_array(value);\n\tvar version = source(0);\n\n\tif (is_proxied_array) {\n\t\t// We need to create the length source eagerly to ensure that\n\t\t// mutations to the array are properly synced with our proxy\n\t\tsources.set('length', source(/** @type {any[]} */ (value).length, stack));\n\t}\n\n\t/** @type {ProxyMetadata} */\n\tvar metadata;\n\n\tif (DEV) {\n\t\tmetadata = {\n\t\t\tparent,\n\t\t\towners: null\n\t\t};\n\n\t\tif (prev) {\n\t\t\t// Reuse owners from previous state; necessary because reassignment is not guaranteed to have correct component context.\n\t\t\t// If no previous proxy exists we play it safe and assume ownerless state\n\t\t\t// @ts-expect-error\n\t\t\tconst prev_owners = prev.v?.[STATE_SYMBOL_METADATA]?.owners;\n\t\t\tmetadata.owners = prev_owners ? new Set(prev_owners) : null;\n\t\t} else {\n\t\t\tmetadata.owners =\n\t\t\t\tparent === null\n\t\t\t\t\t? component_context !== null\n\t\t\t\t\t\t? new Set([component_context.function])\n\t\t\t\t\t\t: null\n\t\t\t\t\t: new Set();\n\t\t}\n\t}\n\n\treturn new Proxy(/** @type {any} */ (value), {\n\t\tdefineProperty(_, prop, descriptor) {\n\t\t\tif (\n\t\t\t\t!('value' in descriptor) ||\n\t\t\t\tdescriptor.configurable === false ||\n\t\t\t\tdescriptor.enumerable === false ||\n\t\t\t\tdescriptor.writable === false\n\t\t\t) {\n\t\t\t\t// we disallow non-basic descriptors, because unless they are applied to the\n\t\t\t\t// target object — which we avoid, so that state can be forked — we will run\n\t\t\t\t// afoul of the various invariants\n\t\t\t\t// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Proxy/Proxy/getOwnPropertyDescriptor#invariants\n\t\t\t\te.state_descriptors_fixed();\n\t\t\t}\n\n\t\t\tvar s = sources.get(prop);\n\n\t\t\tif (s === undefined) {\n\t\t\t\ts = source(descriptor.value, stack);\n\t\t\t\tsources.set(prop, s);\n\t\t\t} else {\n\t\t\t\tset(s, proxy(descriptor.value, metadata));\n\t\t\t}\n\n\t\t\treturn true;\n\t\t},\n\n\t\tdeleteProperty(target, prop) {\n\t\t\tvar s = sources.get(prop);\n\n\t\t\tif (s === undefined) {\n\t\t\t\tif (prop in target) {\n\t\t\t\t\tsources.set(prop, source(UNINITIALIZED, stack));\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\t// When working with arrays, we need to also ensure we update the length when removing\n\t\t\t\t// an indexed property\n\t\t\t\tif (is_proxied_array && typeof prop === 'string') {\n\t\t\t\t\tvar ls = /** @type {Source<number>} */ (sources.get('length'));\n\t\t\t\t\tvar n = Number(prop);\n\n\t\t\t\t\tif (Number.isInteger(n) && n < ls.v) {\n\t\t\t\t\t\tset(ls, n);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tset(s, UNINITIALIZED);\n\t\t\t\tupdate_version(version);\n\t\t\t}\n\n\t\t\treturn true;\n\t\t},\n\n\t\tget(target, prop, receiver) {\n\t\t\tif (DEV && prop === STATE_SYMBOL_METADATA) {\n\t\t\t\treturn metadata;\n\t\t\t}\n\n\t\t\tif (prop === STATE_SYMBOL) {\n\t\t\t\treturn value;\n\t\t\t}\n\n\t\t\tvar s = sources.get(prop);\n\t\t\tvar exists = prop in target;\n\n\t\t\t// create a source, but only if it's an own property and not a prototype property\n\t\t\tif (s === undefined && (!exists || get_descriptor(target, prop)?.writable)) {\n\t\t\t\ts = source(proxy(exists ? target[prop] : UNINITIALIZED, metadata), stack);\n\t\t\t\tsources.set(prop, s);\n\t\t\t}\n\n\t\t\tif (s !== undefined) {\n\t\t\t\tvar v = get(s);\n\n\t\t\t\t// In case of something like `foo = bar.map(...)`, foo would have ownership\n\t\t\t\t// of the array itself, while the individual items would have ownership\n\t\t\t\t// of the component that created bar. That means if we later do `foo[0].baz = 42`,\n\t\t\t\t// we could get a false-positive ownership violation, since the two proxies\n\t\t\t\t// are not connected to each other via the parent metadata relationship.\n\t\t\t\t// For this reason, we need to widen the ownership of the children\n\t\t\t\t// upon access when we detect they are not connected.\n\t\t\t\tif (DEV) {\n\t\t\t\t\t/** @type {ProxyMetadata | undefined} */\n\t\t\t\t\tvar prop_metadata = v?.[STATE_SYMBOL_METADATA];\n\t\t\t\t\tif (prop_metadata && prop_metadata?.parent !== metadata) {\n\t\t\t\t\t\twiden_ownership(metadata, prop_metadata);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\treturn v === UNINITIALIZED ? undefined : v;\n\t\t\t}\n\n\t\t\treturn Reflect.get(target, prop, receiver);\n\t\t},\n\n\t\tgetOwnPropertyDescriptor(target, prop) {\n\t\t\tvar descriptor = Reflect.getOwnPropertyDescriptor(target, prop);\n\n\t\t\tif (descriptor && 'value' in descriptor) {\n\t\t\t\tvar s = sources.get(prop);\n\t\t\t\tif (s) descriptor.value = get(s);\n\t\t\t} else if (descriptor === undefined) {\n\t\t\t\tvar source = sources.get(prop);\n\t\t\t\tvar value = source?.v;\n\n\t\t\t\tif (source !== undefined && value !== UNINITIALIZED) {\n\t\t\t\t\treturn {\n\t\t\t\t\t\tenumerable: true,\n\t\t\t\t\t\tconfigurable: true,\n\t\t\t\t\t\tvalue,\n\t\t\t\t\t\twritable: true\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t}\n\n\t\t\treturn descriptor;\n\t\t},\n\n\t\thas(target, prop) {\n\t\t\tif (DEV && prop === STATE_SYMBOL_METADATA) {\n\t\t\t\treturn true;\n\t\t\t}\n\n\t\t\tif (prop === STATE_SYMBOL) {\n\t\t\t\treturn true;\n\t\t\t}\n\n\t\t\tvar s = sources.get(prop);\n\t\t\tvar has = (s !== undefined && s.v !== UNINITIALIZED) || Reflect.has(target, prop);\n\n\t\t\tif (\n\t\t\t\ts !== undefined ||\n\t\t\t\t(active_effect !== null && (!has || get_descriptor(target, prop)?.writable))\n\t\t\t) {\n\t\t\t\tif (s === undefined) {\n\t\t\t\t\ts = source(has ? proxy(target[prop], metadata) : UNINITIALIZED, stack);\n\t\t\t\t\tsources.set(prop, s);\n\t\t\t\t}\n\n\t\t\t\tvar value = get(s);\n\t\t\t\tif (value === UNINITIALIZED) {\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\treturn has;\n\t\t},\n\n\t\tset(target, prop, value, receiver) {\n\t\t\tvar s = sources.get(prop);\n\t\t\tvar has = prop in target;\n\n\t\t\t// variable.length = value -> clear all signals with index >= value\n\t\t\tif (is_proxied_array && prop === 'length') {\n\t\t\t\tfor (var i = value; i < /** @type {Source<number>} */ (s).v; i += 1) {\n\t\t\t\t\tvar other_s = sources.get(i + '');\n\t\t\t\t\tif (other_s !== undefined) {\n\t\t\t\t\t\tset(other_s, UNINITIALIZED);\n\t\t\t\t\t} else if (i in target) {\n\t\t\t\t\t\t// If the item exists in the original, we need to create a uninitialized source,\n\t\t\t\t\t\t// else a later read of the property would result in a source being created with\n\t\t\t\t\t\t// the value of the original item at that index.\n\t\t\t\t\t\tother_s = source(UNINITIALIZED, stack);\n\t\t\t\t\t\tsources.set(i + '', other_s);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// If we haven't yet created a source for this property, we need to ensure\n\t\t\t// we do so otherwise if we read it later, then the write won't be tracked and\n\t\t\t// the heuristics of effects will be different vs if we had read the proxied\n\t\t\t// object property before writing to that property.\n\t\t\tif (s === undefined) {\n\t\t\t\tif (!has || get_descriptor(target, prop)?.writable) {\n\t\t\t\t\ts = source(undefined, stack);\n\t\t\t\t\tset(s, proxy(value, metadata));\n\t\t\t\t\tsources.set(prop, s);\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\thas = s.v !== UNINITIALIZED;\n\t\t\t\tset(s, proxy(value, metadata));\n\t\t\t}\n\n\t\t\tif (DEV) {\n\t\t\t\t/** @type {ProxyMetadata | undefined} */\n\t\t\t\tvar prop_metadata = value?.[STATE_SYMBOL_METADATA];\n\t\t\t\tif (prop_metadata && prop_metadata?.parent !== metadata) {\n\t\t\t\t\twiden_ownership(metadata, prop_metadata);\n\t\t\t\t}\n\t\t\t\tcheck_ownership(metadata);\n\t\t\t}\n\n\t\t\tvar descriptor = Reflect.getOwnPropertyDescriptor(target, prop);\n\n\t\t\t// Set the new value before updating any signals so that any listeners get the new value\n\t\t\tif (descriptor?.set) {\n\t\t\t\tdescriptor.set.call(receiver, value);\n\t\t\t}\n\n\t\t\tif (!has) {\n\t\t\t\t// If we have mutated an array directly, we might need to\n\t\t\t\t// signal that length has also changed. Do it before updating metadata\n\t\t\t\t// to ensure that iterating over the array as a result of a metadata update\n\t\t\t\t// will not cause the length to be out of sync.\n\t\t\t\tif (is_proxied_array && typeof prop === 'string') {\n\t\t\t\t\tvar ls = /** @type {Source<number>} */ (sources.get('length'));\n\t\t\t\t\tvar n = Number(prop);\n\n\t\t\t\t\tif (Number.isInteger(n) && n >= ls.v) {\n\t\t\t\t\t\tset(ls, n + 1);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tupdate_version(version);\n\t\t\t}\n\n\t\t\treturn true;\n\t\t},\n\n\t\townKeys(target) {\n\t\t\tget(version);\n\n\t\t\tvar own_keys = Reflect.ownKeys(target).filter((key) => {\n\t\t\t\tvar source = sources.get(key);\n\t\t\t\treturn source === undefined || source.v !== UNINITIALIZED;\n\t\t\t});\n\n\t\t\tfor (var [key, source] of sources) {\n\t\t\t\tif (source.v !== UNINITIALIZED && !(key in target)) {\n\t\t\t\t\town_keys.push(key);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\treturn own_keys;\n\t\t},\n\n\t\tsetPrototypeOf() {\n\t\t\te.state_prototype_fixed();\n\t\t}\n\t});\n}\n\n/**\n * @param {Source<number>} signal\n * @param {1 | -1} [d]\n */\nfunction update_version(signal, d = 1) {\n\tset(signal, signal.v + d);\n}\n\n/**\n * @param {any} value\n */\nexport function get_proxied_value(value) {\n\tif (value !== null && typeof value === 'object' && STATE_SYMBOL in value) {\n\t\treturn value[STATE_SYMBOL];\n\t}\n\n\treturn value;\n}\n\n/**\n * @param {any} a\n * @param {any} b\n */\nexport function is(a, b) {\n\treturn Object.is(get_proxied_value(a), get_proxied_value(b));\n}\n", "import * as w from '../warnings.js';\nimport { get_proxied_value } from '../proxy.js';\n\nexport function init_array_prototype_warnings() {\n\tconst array_prototype = Array.prototype;\n\t// The REPL ends up here over and over, and this prevents it from adding more and more patches\n\t// of the same kind to the prototype, which would slow down everything over time.\n\t// @ts-expect-error\n\tconst cleanup = Array.__svelte_cleanup;\n\tif (cleanup) {\n\t\tcleanup();\n\t}\n\n\tconst { indexOf, lastIndexOf, includes } = array_prototype;\n\n\tarray_prototype.indexOf = function (item, from_index) {\n\t\tconst index = indexOf.call(this, item, from_index);\n\n\t\tif (index === -1) {\n\t\t\tfor (let i = from_index ?? 0; i < this.length; i += 1) {\n\t\t\t\tif (get_proxied_value(this[i]) === item) {\n\t\t\t\t\tw.state_proxy_equality_mismatch('array.indexOf(...)');\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\treturn index;\n\t};\n\n\tarray_prototype.lastIndexOf = function (item, from_index) {\n\t\t// we need to specify this.length - 1 because it's probably using something like\n\t\t// `arguments` inside so passing undefined is different from not passing anything\n\t\tconst index = lastIndexOf.call(this, item, from_index ?? this.length - 1);\n\n\t\tif (index === -1) {\n\t\t\tfor (let i = 0; i <= (from_index ?? this.length - 1); i += 1) {\n\t\t\t\tif (get_proxied_value(this[i]) === item) {\n\t\t\t\t\tw.state_proxy_equality_mismatch('array.lastIndexOf(...)');\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\treturn index;\n\t};\n\n\tarray_prototype.includes = function (item, from_index) {\n\t\tconst has = includes.call(this, item, from_index);\n\n\t\tif (!has) {\n\t\t\tfor (let i = 0; i < this.length; i += 1) {\n\t\t\t\tif (get_proxied_value(this[i]) === item) {\n\t\t\t\t\tw.state_proxy_equality_mismatch('array.includes(...)');\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\treturn has;\n\t};\n\n\t// @ts-expect-error\n\tArray.__svelte_cleanup = () => {\n\t\tarray_prototype.indexOf = indexOf;\n\t\tarray_prototype.lastIndexOf = lastIndexOf;\n\t\tarray_prototype.includes = includes;\n\t};\n}\n\n/**\n * @param {any} a\n * @param {any} b\n * @param {boolean} equal\n * @returns {boolean}\n */\nexport function strict_equals(a, b, equal = true) {\n\t// try-catch needed because this tries to read properties of `a` and `b`,\n\t// which could be disallowed for example in a secure context\n\ttry {\n\t\tif ((a === b) !== (get_proxied_value(a) === get_proxied_value(b))) {\n\t\t\tw.state_proxy_equality_mismatch(equal ? '===' : '!==');\n\t\t}\n\t} catch {}\n\n\treturn (a === b) === equal;\n}\n\n/**\n * @param {any} a\n * @param {any} b\n * @param {boolean} equal\n * @returns {boolean}\n */\nexport function equals(a, b, equal = true) {\n\tif ((a == b) !== (get_proxied_value(a) == get_proxied_value(b))) {\n\t\tw.state_proxy_equality_mismatch(equal ? '==' : '!=');\n\t}\n\n\treturn (a == b) === equal;\n}\n", "/** @import { TemplateNode } from '#client' */\nimport { hydrate_node, hydrating, set_hydrate_node } from './hydration.js';\nimport { DEV } from 'esm-env';\nimport { init_array_prototype_warnings } from '../dev/equality.js';\nimport { get_descriptor } from '../../shared/utils.js';\n\n// export these for reference in the compiled code, making global name deduplication unnecessary\n/** @type {Window} */\nexport var $window;\n\n/** @type {Document} */\nexport var $document;\n\n/** @type {() => Node | null} */\nvar first_child_getter;\n/** @type {() => Node | null} */\nvar next_sibling_getter;\n\n/**\n * Initialize these lazily to avoid issues when using the runtime in a server context\n * where these globals are not available while avoiding a separate server entry point\n */\nexport function init_operations() {\n\tif ($window !== undefined) {\n\t\treturn;\n\t}\n\n\t$window = window;\n\t$document = document;\n\n\tvar element_prototype = Element.prototype;\n\tvar node_prototype = Node.prototype;\n\n\t// @ts-ignore\n\tfirst_child_getter = get_descriptor(node_prototype, 'firstChild').get;\n\t// @ts-ignore\n\tnext_sibling_getter = get_descriptor(node_prototype, 'nextSibling').get;\n\n\t// the following assignments improve perf of lookups on DOM nodes\n\t// @ts-expect-error\n\telement_prototype.__click = undefined;\n\t// @ts-expect-error\n\telement_prototype.__className = '';\n\t// @ts-expect-error\n\telement_prototype.__attributes = null;\n\t// @ts-expect-error\n\telement_prototype.__styles = null;\n\t// @ts-expect-error\n\telement_prototype.__e = undefined;\n\n\t// @ts-expect-error\n\tText.prototype.__t = undefined;\n\n\tif (DEV) {\n\t\t// @ts-expect-error\n\t\telement_prototype.__svelte_meta = null;\n\n\t\tinit_array_prototype_warnings();\n\t}\n}\n\n/**\n * @param {string} value\n * @returns {Text}\n */\nexport function create_text(value = '') {\n\treturn document.createTextNode(value);\n}\n\n/**\n * @template {Node} N\n * @param {N} node\n * @returns {Node | null}\n */\n/*@__NO_SIDE_EFFECTS__*/\nexport function get_first_child(node) {\n\treturn first_child_getter.call(node);\n}\n\n/**\n * @template {Node} N\n * @param {N} node\n * @returns {Node | null}\n */\n/*@__NO_SIDE_EFFECTS__*/\nexport function get_next_sibling(node) {\n\treturn next_sibling_getter.call(node);\n}\n\n/**\n * Don't mark this as side-effect-free, hydration needs to walk all nodes\n * @template {Node} N\n * @param {N} node\n * @param {boolean} is_text\n * @returns {Node | null}\n */\nexport function child(node, is_text) {\n\tif (!hydrating) {\n\t\treturn get_first_child(node);\n\t}\n\n\tvar child = /** @type {TemplateNode} */ (get_first_child(hydrate_node));\n\n\t// Child can be null if we have an element with a single child, like `<p>{text}</p>`, where `text` is empty\n\tif (child === null) {\n\t\tchild = hydrate_node.appendChild(create_text());\n\t} else if (is_text && child.nodeType !== 3) {\n\t\tvar text = create_text();\n\t\tchild?.before(text);\n\t\tset_hydrate_node(text);\n\t\treturn text;\n\t}\n\n\tset_hydrate_node(child);\n\treturn child;\n}\n\n/**\n * Don't mark this as side-effect-free, hydration needs to walk all nodes\n * @param {DocumentFragment | TemplateNode[]} fragment\n * @param {boolean} is_text\n * @returns {Node | null}\n */\nexport function first_child(fragment, is_text) {\n\tif (!hydrating) {\n\t\t// when not hydrating, `fragment` is a `DocumentFragment` (the result of calling `open_frag`)\n\t\tvar first = /** @type {DocumentFragment} */ (get_first_child(/** @type {Node} */ (fragment)));\n\n\t\t// TODO prevent user comments with the empty string when preserveComments is true\n\t\tif (first instanceof Comment && first.data === '') return get_next_sibling(first);\n\n\t\treturn first;\n\t}\n\n\t// if an {expression} is empty during SSR, there might be no\n\t// text node to hydrate — we must therefore create one\n\tif (is_text && hydrate_node?.nodeType !== 3) {\n\t\tvar text = create_text();\n\n\t\thydrate_node?.before(text);\n\t\tset_hydrate_node(text);\n\t\treturn text;\n\t}\n\n\treturn hydrate_node;\n}\n\n/**\n * Don't mark this as side-effect-free, hydration needs to walk all nodes\n * @param {TemplateNode} node\n * @param {number} count\n * @param {boolean} is_text\n * @returns {Node | null}\n */\nexport function sibling(node, count = 1, is_text = false) {\n\tlet next_sibling = hydrating ? hydrate_node : node;\n\tvar last_sibling;\n\n\twhile (count--) {\n\t\tlast_sibling = next_sibling;\n\t\tnext_sibling = /** @type {TemplateNode} */ (get_next_sibling(next_sibling));\n\t}\n\n\tif (!hydrating) {\n\t\treturn next_sibling;\n\t}\n\n\tvar type = next_sibling?.nodeType;\n\n\t// if a sibling {expression} is empty during SSR, there might be no\n\t// text node to hydrate — we must therefore create one\n\tif (is_text && type !== 3) {\n\t\tvar text = create_text();\n\t\t// If the next sibling is `null` and we're handling text then it's because\n\t\t// the SSR content was empty for the text, so we need to generate a new text\n\t\t// node and insert it after the last sibling\n\t\tif (next_sibling === null) {\n\t\t\tlast_sibling?.after(text);\n\t\t} else {\n\t\t\tnext_sibling.before(text);\n\t\t}\n\t\tset_hydrate_node(text);\n\t\treturn text;\n\t}\n\n\tset_hydrate_node(next_sibling);\n\treturn /** @type {TemplateNode} */ (next_sibling);\n}\n\n/**\n * @template {Node} N\n * @param {N} node\n * @returns {void}\n */\nexport function clear_text_content(node) {\n\tnode.textContent = '';\n}\n", "/** @import { ComponentContext, ComponentContextLegacy, Derived, Effect, TemplateNode, TransitionManager } from '#client' */\nimport {\n\tcheck_dirtiness,\n\tcomponent_context,\n\tactive_effect,\n\tactive_reaction,\n\tdev_current_component_function,\n\tupdate_effect,\n\tget,\n\tis_destroying_effect,\n\tis_flushing_effect,\n\tremove_reactions,\n\tschedule_effect,\n\tset_active_reaction,\n\tset_is_destroying_effect,\n\tset_is_flushing_effect,\n\tset_signal_status,\n\tuntrack,\n\tskip_reaction\n} from '../runtime.js';\nimport {\n\tDIRTY,\n\tBRANCH_EFFECT,\n\tRENDER_EFFECT,\n\tEFFECT,\n\tDESTROYED,\n\tINERT,\n\tEFFECT_RAN,\n\tBLOCK_EFFECT,\n\tROOT_EFFECT,\n\tEFFECT_TRANSPARENT,\n\tDERIVED,\n\tUNOWNED,\n\tCLEAN,\n\tINSPECT_EFFECT,\n\tHEAD_EFFECT,\n\tMAYBE_DIRTY,\n\tEFFECT_HAS_DERIVED,\n\tBOUNDARY_EFFECT\n} from '../constants.js';\nimport { set } from './sources.js';\nimport * as e from '../errors.js';\nimport { DEV } from 'esm-env';\nimport { define_property } from '../../shared/utils.js';\nimport { get_next_sibling } from '../dom/operations.js';\nimport { destroy_derived } from './deriveds.js';\n\n/**\n * @param {'$effect' | '$effect.pre' | '$inspect'} rune\n */\nexport function validate_effect(rune) {\n\tif (active_effect === null && active_reaction === null) {\n\t\te.effect_orphan(rune);\n\t}\n\n\tif (active_reaction !== null && (active_reaction.f & UNOWNED) !== 0) {\n\t\te.effect_in_unowned_derived();\n\t}\n\n\tif (is_destroying_effect) {\n\t\te.effect_in_teardown(rune);\n\t}\n}\n\n/**\n * @param {Effect} effect\n * @param {Effect} parent_effect\n */\nfunction push_effect(effect, parent_effect) {\n\tvar parent_last = parent_effect.last;\n\tif (parent_last === null) {\n\t\tparent_effect.last = parent_effect.first = effect;\n\t} else {\n\t\tparent_last.next = effect;\n\t\teffect.prev = parent_last;\n\t\tparent_effect.last = effect;\n\t}\n}\n\n/**\n * @param {number} type\n * @param {null | (() => void | (() => void))} fn\n * @param {boolean} sync\n * @param {boolean} push\n * @returns {Effect}\n */\nfunction create_effect(type, fn, sync, push = true) {\n\tvar is_root = (type & ROOT_EFFECT) !== 0;\n\tvar parent_effect = active_effect;\n\n\tif (DEV) {\n\t\t// Ensure the parent is never an inspect effect\n\t\twhile (parent_effect !== null && (parent_effect.f & INSPECT_EFFECT) !== 0) {\n\t\t\tparent_effect = parent_effect.parent;\n\t\t}\n\t}\n\n\t/** @type {Effect} */\n\tvar effect = {\n\t\tctx: component_context,\n\t\tdeps: null,\n\t\tderiveds: null,\n\t\tnodes_start: null,\n\t\tnodes_end: null,\n\t\tf: type | DIRTY,\n\t\tfirst: null,\n\t\tfn,\n\t\tlast: null,\n\t\tnext: null,\n\t\tparent: is_root ? null : parent_effect,\n\t\tprev: null,\n\t\tteardown: null,\n\t\ttransitions: null,\n\t\twv: 0\n\t};\n\n\tif (DEV) {\n\t\teffect.component_function = dev_current_component_function;\n\t}\n\n\tif (sync) {\n\t\tvar previously_flushing_effect = is_flushing_effect;\n\n\t\ttry {\n\t\t\tset_is_flushing_effect(true);\n\t\t\tupdate_effect(effect);\n\t\t\teffect.f |= EFFECT_RAN;\n\t\t} catch (e) {\n\t\t\tdestroy_effect(effect);\n\t\t\tthrow e;\n\t\t} finally {\n\t\t\tset_is_flushing_effect(previously_flushing_effect);\n\t\t}\n\t} else if (fn !== null) {\n\t\tschedule_effect(effect);\n\t}\n\n\t// if an effect has no dependencies, no DOM and no teardown function,\n\t// don't bother adding it to the effect tree\n\tvar inert =\n\t\tsync &&\n\t\teffect.deps === null &&\n\t\teffect.first === null &&\n\t\teffect.nodes_start === null &&\n\t\teffect.teardown === null &&\n\t\t(effect.f & (EFFECT_HAS_DERIVED | BOUNDARY_EFFECT)) === 0;\n\n\tif (!inert && !is_root && push) {\n\t\tif (parent_effect !== null) {\n\t\t\tpush_effect(effect, parent_effect);\n\t\t}\n\n\t\t// if we're in a derived, add the effect there too\n\t\tif (active_reaction !== null && (active_reaction.f & DERIVED) !== 0) {\n\t\t\tvar derived = /** @type {Derived} */ (active_reaction);\n\t\t\t(derived.children ??= []).push(effect);\n\t\t}\n\t}\n\n\treturn effect;\n}\n\n/**\n * Internal representation of `$effect.tracking()`\n * @returns {boolean}\n */\nexport function effect_tracking() {\n\tif (active_reaction === null) {\n\t\treturn false;\n\t}\n\n\t// If it's skipped, that's because we're inside an unowned\n\t// that is not being tracked by another reaction\n\treturn !skip_reaction;\n}\n\n/**\n * @param {() => void} fn\n */\nexport function teardown(fn) {\n\tconst effect = create_effect(RENDER_EFFECT, null, false);\n\tset_signal_status(effect, CLEAN);\n\teffect.teardown = fn;\n\treturn effect;\n}\n\n/**\n * Internal representation of `$effect(...)`\n * @param {() => void | (() => void)} fn\n */\nexport function user_effect(fn) {\n\tvalidate_effect('$effect');\n\n\t// Non-nested `$effect(...)` in a component should be deferred\n\t// until the component is mounted\n\tvar defer =\n\t\tactive_effect !== null &&\n\t\t(active_effect.f & BRANCH_EFFECT) !== 0 &&\n\t\tcomponent_context !== null &&\n\t\t!component_context.m;\n\n\tif (DEV) {\n\t\tdefine_property(fn, 'name', {\n\t\t\tvalue: '$effect'\n\t\t});\n\t}\n\n\tif (defer) {\n\t\tvar context = /** @type {ComponentContext} */ (component_context);\n\t\t(context.e ??= []).push({\n\t\t\tfn,\n\t\t\teffect: active_effect,\n\t\t\treaction: active_reaction\n\t\t});\n\t} else {\n\t\tvar signal = effect(fn);\n\t\treturn signal;\n\t}\n}\n\n/**\n * Internal representation of `$effect.pre(...)`\n * @param {() => void | (() => void)} fn\n * @returns {Effect}\n */\nexport function user_pre_effect(fn) {\n\tvalidate_effect('$effect.pre');\n\tif (DEV) {\n\t\tdefine_property(fn, 'name', {\n\t\t\tvalue: '$effect.pre'\n\t\t});\n\t}\n\treturn render_effect(fn);\n}\n\n/** @param {() => void | (() => void)} fn */\nexport function inspect_effect(fn) {\n\treturn create_effect(INSPECT_EFFECT, fn, true);\n}\n\n/**\n * Internal representation of `$effect.root(...)`\n * @param {() => void | (() => void)} fn\n * @returns {() => void}\n */\nexport function effect_root(fn) {\n\tconst effect = create_effect(ROOT_EFFECT, fn, true);\n\n\treturn () => {\n\t\tdestroy_effect(effect);\n\t};\n}\n\n/**\n * An effect root whose children can transition out\n * @param {() => void} fn\n * @returns {(options?: { outro?: boolean }) => Promise<void>}\n */\nexport function component_root(fn) {\n\tconst effect = create_effect(ROOT_EFFECT, fn, true);\n\n\treturn (options = {}) => {\n\t\treturn new Promise((fulfil) => {\n\t\t\tif (options.outro) {\n\t\t\t\tpause_effect(effect, () => {\n\t\t\t\t\tdestroy_effect(effect);\n\t\t\t\t\tfulfil(undefined);\n\t\t\t\t});\n\t\t\t} else {\n\t\t\t\tdestroy_effect(effect);\n\t\t\t\tfulfil(undefined);\n\t\t\t}\n\t\t});\n\t};\n}\n\n/**\n * @param {() => void | (() => void)} fn\n * @returns {Effect}\n */\nexport function effect(fn) {\n\treturn create_effect(EFFECT, fn, false);\n}\n\n/**\n * Internal representation of `$: ..`\n * @param {() => any} deps\n * @param {() => void | (() => void)} fn\n */\nexport function legacy_pre_effect(deps, fn) {\n\tvar context = /** @type {ComponentContextLegacy} */ (component_context);\n\n\t/** @type {{ effect: null | Effect, ran: boolean }} */\n\tvar token = { effect: null, ran: false };\n\tcontext.l.r1.push(token);\n\n\ttoken.effect = render_effect(() => {\n\t\tdeps();\n\n\t\t// If this legacy pre effect has already run before the end of the reset, then\n\t\t// bail out to emulate the same behavior.\n\t\tif (token.ran) return;\n\n\t\ttoken.ran = true;\n\t\tset(context.l.r2, true);\n\t\tuntrack(fn);\n\t});\n}\n\nexport function legacy_pre_effect_reset() {\n\tvar context = /** @type {ComponentContextLegacy} */ (component_context);\n\n\trender_effect(() => {\n\t\tif (!get(context.l.r2)) return;\n\n\t\t// Run dirty `$:` statements\n\t\tfor (var token of context.l.r1) {\n\t\t\tvar effect = token.effect;\n\n\t\t\t// If the effect is CLEAN, then make it MAYBE_DIRTY. This ensures we traverse through\n\t\t\t// the effects dependencies and correctly ensure each dependency is up-to-date.\n\t\t\tif ((effect.f & CLEAN) !== 0) {\n\t\t\t\tset_signal_status(effect, MAYBE_DIRTY);\n\t\t\t}\n\n\t\t\tif (check_dirtiness(effect)) {\n\t\t\t\tupdate_effect(effect);\n\t\t\t}\n\n\t\t\ttoken.ran = false;\n\t\t}\n\n\t\tcontext.l.r2.v = false; // set directly to avoid rerunning this effect\n\t});\n}\n\n/**\n * @param {() => void | (() => void)} fn\n * @returns {Effect}\n */\nexport function render_effect(fn) {\n\treturn create_effect(RENDER_EFFECT, fn, true);\n}\n\n/**\n * @param {() => void | (() => void)} fn\n * @returns {Effect}\n */\nexport function template_effect(fn) {\n\tif (DEV) {\n\t\tdefine_property(fn, 'name', {\n\t\t\tvalue: '{expression}'\n\t\t});\n\t}\n\treturn block(fn);\n}\n\n/**\n * @param {(() => void)} fn\n * @param {number} flags\n */\nexport function block(fn, flags = 0) {\n\treturn create_effect(RENDER_EFFECT | BLOCK_EFFECT | flags, fn, true);\n}\n\n/**\n * @param {(() => void)} fn\n * @param {boolean} [push]\n */\nexport function branch(fn, push = true) {\n\treturn create_effect(RENDER_EFFECT | BRANCH_EFFECT, fn, true, push);\n}\n\n/**\n * @param {Effect} effect\n */\nexport function execute_effect_teardown(effect) {\n\tvar teardown = effect.teardown;\n\tif (teardown !== null) {\n\t\tconst previously_destroying_effect = is_destroying_effect;\n\t\tconst previous_reaction = active_reaction;\n\t\tset_is_destroying_effect(true);\n\t\tset_active_reaction(null);\n\t\ttry {\n\t\t\tteardown.call(null);\n\t\t} finally {\n\t\t\tset_is_destroying_effect(previously_destroying_effect);\n\t\t\tset_active_reaction(previous_reaction);\n\t\t}\n\t}\n}\n\n/**\n * @param {Effect} signal\n * @returns {void}\n */\nexport function destroy_effect_deriveds(signal) {\n\tvar deriveds = signal.deriveds;\n\n\tif (deriveds !== null) {\n\t\tsignal.deriveds = null;\n\n\t\tfor (var i = 0; i < deriveds.length; i += 1) {\n\t\t\tdestroy_derived(deriveds[i]);\n\t\t}\n\t}\n}\n\n/**\n * @param {Effect} signal\n * @param {boolean} remove_dom\n * @returns {void}\n */\nexport function destroy_effect_children(signal, remove_dom = false) {\n\tvar effect = signal.first;\n\tsignal.first = signal.last = null;\n\n\twhile (effect !== null) {\n\t\tvar next = effect.next;\n\t\tdestroy_effect(effect, remove_dom);\n\t\teffect = next;\n\t}\n}\n\n/**\n * @param {Effect} signal\n * @returns {void}\n */\nexport function destroy_block_effect_children(signal) {\n\tvar effect = signal.first;\n\n\twhile (effect !== null) {\n\t\tvar next = effect.next;\n\t\tif ((effect.f & BRANCH_EFFECT) === 0) {\n\t\t\tdestroy_effect(effect);\n\t\t}\n\t\teffect = next;\n\t}\n}\n\n/**\n * @param {Effect} effect\n * @param {boolean} [remove_dom]\n * @returns {void}\n */\nexport function destroy_effect(effect, remove_dom = true) {\n\tvar removed = false;\n\n\tif ((remove_dom || (effect.f & HEAD_EFFECT) !== 0) && effect.nodes_start !== null) {\n\t\t/** @type {TemplateNode | null} */\n\t\tvar node = effect.nodes_start;\n\t\tvar end = effect.nodes_end;\n\n\t\twhile (node !== null) {\n\t\t\t/** @type {TemplateNode | null} */\n\t\t\tvar next = node === end ? null : /** @type {TemplateNode} */ (get_next_sibling(node));\n\n\t\t\tnode.remove();\n\t\t\tnode = next;\n\t\t}\n\n\t\tremoved = true;\n\t}\n\n\tdestroy_effect_children(effect, remove_dom && !removed);\n\tdestroy_effect_deriveds(effect);\n\tremove_reactions(effect, 0);\n\tset_signal_status(effect, DESTROYED);\n\n\tvar transitions = effect.transitions;\n\n\tif (transitions !== null) {\n\t\tfor (const transition of transitions) {\n\t\t\ttransition.stop();\n\t\t}\n\t}\n\n\texecute_effect_teardown(effect);\n\n\tvar parent = effect.parent;\n\n\t// If the parent doesn't have any children, then skip this work altogether\n\tif (parent !== null && parent.first !== null) {\n\t\tunlink_effect(effect);\n\t}\n\n\tif (DEV) {\n\t\teffect.component_function = null;\n\t}\n\n\t// `first` and `child` are nulled out in destroy_effect_children\n\t// we don't null out `parent` so that error propagation can work correctly\n\teffect.next =\n\t\teffect.prev =\n\t\teffect.teardown =\n\t\teffect.ctx =\n\t\teffect.deps =\n\t\teffect.fn =\n\t\teffect.nodes_start =\n\t\teffect.nodes_end =\n\t\t\tnull;\n}\n\n/**\n * Detach an effect from the effect tree, freeing up memory and\n * reducing the amount of work that happens on subsequent traversals\n * @param {Effect} effect\n */\nexport function unlink_effect(effect) {\n\tvar parent = effect.parent;\n\tvar prev = effect.prev;\n\tvar next = effect.next;\n\n\tif (prev !== null) prev.next = next;\n\tif (next !== null) next.prev = prev;\n\n\tif (parent !== null) {\n\t\tif (parent.first === effect) parent.first = next;\n\t\tif (parent.last === effect) parent.last = prev;\n\t}\n}\n\n/**\n * When a block effect is removed, we don't immediately destroy it or yank it\n * out of the DOM, because it might have transitions. Instead, we 'pause' it.\n * It stays around (in memory, and in the DOM) until outro transitions have\n * completed, and if the state change is reversed then we _resume_ it.\n * A paused effect does not update, and the DOM subtree becomes inert.\n * @param {Effect} effect\n * @param {() => void} [callback]\n */\nexport function pause_effect(effect, callback) {\n\t/** @type {TransitionManager[]} */\n\tvar transitions = [];\n\n\tpause_children(effect, transitions, true);\n\n\trun_out_transitions(transitions, () => {\n\t\tdestroy_effect(effect);\n\t\tif (callback) callback();\n\t});\n}\n\n/**\n * @param {TransitionManager[]} transitions\n * @param {() => void} fn\n */\nexport function run_out_transitions(transitions, fn) {\n\tvar remaining = transitions.length;\n\tif (remaining > 0) {\n\t\tvar check = () => --remaining || fn();\n\t\tfor (var transition of transitions) {\n\t\t\ttransition.out(check);\n\t\t}\n\t} else {\n\t\tfn();\n\t}\n}\n\n/**\n * @param {Effect} effect\n * @param {TransitionManager[]} transitions\n * @param {boolean} local\n */\nexport function pause_children(effect, transitions, local) {\n\tif ((effect.f & INERT) !== 0) return;\n\teffect.f ^= INERT;\n\n\tif (effect.transitions !== null) {\n\t\tfor (const transition of effect.transitions) {\n\t\t\tif (transition.is_global || local) {\n\t\t\t\ttransitions.push(transition);\n\t\t\t}\n\t\t}\n\t}\n\n\tvar child = effect.first;\n\n\twhile (child !== null) {\n\t\tvar sibling = child.next;\n\t\tvar transparent = (child.f & EFFECT_TRANSPARENT) !== 0 || (child.f & BRANCH_EFFECT) !== 0;\n\t\t// TODO we don't need to call pause_children recursively with a linked list in place\n\t\t// it's slightly more involved though as we have to account for `transparent` changing\n\t\t// through the tree.\n\t\tpause_children(child, transitions, transparent ? local : false);\n\t\tchild = sibling;\n\t}\n}\n\n/**\n * The opposite of `pause_effect`. We call this if (for example)\n * `x` becomes falsy then truthy: `{#if x}...{/if}`\n * @param {Effect} effect\n */\nexport function resume_effect(effect) {\n\tresume_children(effect, true);\n}\n\n/**\n * @param {Effect} effect\n * @param {boolean} local\n */\nfunction resume_children(effect, local) {\n\tif ((effect.f & INERT) === 0) return;\n\teffect.f ^= INERT;\n\n\t// Ensure the effect is marked as clean again so that any dirty child\n\t// effects can schedule themselves for execution\n\tif ((effect.f & CLEAN) === 0) {\n\t\teffect.f ^= CLEAN;\n\t}\n\n\t// If a dependency of this effect changed while it was paused,\n\t// schedule the effect to update\n\tif (check_dirtiness(effect)) {\n\t\tset_signal_status(effect, DIRTY);\n\t\tschedule_effect(effect);\n\t}\n\n\tvar child = effect.first;\n\n\twhile (child !== null) {\n\t\tvar sibling = child.next;\n\t\tvar transparent = (child.f & EFFECT_TRANSPARENT) !== 0 || (child.f & BRANCH_EFFECT) !== 0;\n\t\t// TODO we don't need to call resume_children recursively with a linked list in place\n\t\t// it's slightly more involved though as we have to account for `transparent` changing\n\t\t// through the tree.\n\t\tresume_children(child, transparent ? local : false);\n\t\tchild = sibling;\n\t}\n\n\tif (effect.transitions !== null) {\n\t\tfor (const transition of effect.transitions) {\n\t\t\tif (transition.is_global || local) {\n\t\t\t\ttransition.in();\n\t\t\t}\n\t\t}\n\t}\n}\n", "/** @import { Derived, Reaction, Signal, Value } from '#client' */\nimport { UNINITIALIZED } from '../../../constants.js';\nimport { snapshot } from '../../shared/clone.js';\nimport { define_property } from '../../shared/utils.js';\nimport { DERIVED, STATE_SYMBOL } from '../constants.js';\nimport { effect_tracking } from '../reactivity/effects.js';\nimport { active_reaction, captured_signals, set_captured_signals, untrack } from '../runtime.js';\n\n/** @type { any } */\nexport let tracing_expressions = null;\n\n/**\n * @param { Value } signal\n * @param { { read: Error[] } } [entry]\n */\nfunction log_entry(signal, entry) {\n\tconst debug = signal.debug;\n\tconst value = signal.trace_need_increase ? signal.trace_v : signal.v;\n\n\tif (value === UNINITIALIZED) {\n\t\treturn;\n\t}\n\n\tif (debug) {\n\t\tvar previous_captured_signals = captured_signals;\n\t\tvar captured = new Set();\n\t\tset_captured_signals(captured);\n\t\ttry {\n\t\t\tuntrack(() => {\n\t\t\t\tdebug();\n\t\t\t});\n\t\t} finally {\n\t\t\tset_captured_signals(previous_captured_signals);\n\t\t}\n\t\tif (captured.size > 0) {\n\t\t\tfor (const dep of captured) {\n\t\t\t\tlog_entry(dep);\n\t\t\t}\n\t\t\treturn;\n\t\t}\n\t}\n\n\tconst type = (signal.f & DERIVED) !== 0 ? '$derived' : '$state';\n\tconst current_reaction = /** @type {Reaction} */ (active_reaction);\n\tconst dirty = signal.wv > current_reaction.wv || current_reaction.wv === 0;\n\n\t// eslint-disable-next-line no-console\n\tconsole.groupCollapsed(\n\t\t`%c${type}`,\n\t\tdirty ? 'color: CornflowerBlue; font-weight: bold' : 'color: grey; font-weight: bold',\n\t\ttypeof value === 'object' && value !== null && STATE_SYMBOL in value\n\t\t\t? snapshot(value, true)\n\t\t\t: value\n\t);\n\n\tif (type === '$derived') {\n\t\tconst deps = new Set(/** @type {Derived} */ (signal).deps);\n\t\tfor (const dep of deps) {\n\t\t\tlog_entry(dep);\n\t\t}\n\t}\n\n\tif (signal.created) {\n\t\t// eslint-disable-next-line no-console\n\t\tconsole.log(signal.created);\n\t}\n\n\tif (signal.updated) {\n\t\t// eslint-disable-next-line no-console\n\t\tconsole.log(signal.updated);\n\t}\n\n\tconst read = entry?.read;\n\n\tif (read && read.length > 0) {\n\t\tfor (var stack of read) {\n\t\t\t// eslint-disable-next-line no-console\n\t\t\tconsole.log(stack);\n\t\t}\n\t}\n\n\t// eslint-disable-next-line no-console\n\tconsole.groupEnd();\n}\n\n/**\n * @template T\n * @param {() => string} label\n * @param {() => T} fn\n */\nexport function trace(label, fn) {\n\tvar previously_tracing_expressions = tracing_expressions;\n\ttry {\n\t\ttracing_expressions = { entries: new Map(), reaction: active_reaction };\n\n\t\tvar start = performance.now();\n\t\tvar value = fn();\n\t\tvar time = (performance.now() - start).toFixed(2);\n\n\t\tif (!effect_tracking()) {\n\t\t\t// eslint-disable-next-line no-console\n\t\t\tconsole.log(`${label()} %cran outside of an effect (${time}ms)`, 'color: grey');\n\t\t} else if (tracing_expressions.entries.size === 0) {\n\t\t\t// eslint-disable-next-line no-console\n\t\t\tconsole.log(`${label()} %cno reactive dependencies (${time}ms)`, 'color: grey');\n\t\t} else {\n\t\t\t// eslint-disable-next-line no-console\n\t\t\tconsole.group(`${label()} %c(${time}ms)`, 'color: grey');\n\n\t\t\tvar entries = tracing_expressions.entries;\n\n\t\t\ttracing_expressions = null;\n\n\t\t\tfor (const [signal, entry] of entries) {\n\t\t\t\tlog_entry(signal, entry);\n\t\t\t}\n\t\t\t// eslint-disable-next-line no-console\n\t\t\tconsole.groupEnd();\n\t\t}\n\n\t\tif (previously_tracing_expressions !== null && tracing_expressions !== null) {\n\t\t\tfor (const [signal, entry] of tracing_expressions.entries) {\n\t\t\t\tvar prev_entry = previously_tracing_expressions.get(signal);\n\n\t\t\t\tif (prev_entry === undefined) {\n\t\t\t\t\tpreviously_tracing_expressions.set(signal, entry);\n\t\t\t\t} else {\n\t\t\t\t\tprev_entry.read.push(...entry.read);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\treturn value;\n\t} finally {\n\t\ttracing_expressions = previously_tracing_expressions;\n\t}\n}\n\n/**\n * @param {string} label\n */\nexport function get_stack(label) {\n\tlet error = Error();\n\tconst stack = error.stack;\n\n\tif (stack) {\n\t\tconst lines = stack.split('\\n');\n\t\tconst new_lines = ['\\n'];\n\n\t\tfor (let i = 0; i < lines.length; i++) {\n\t\t\tconst line = lines[i];\n\n\t\t\tif (line === 'Error') {\n\t\t\t\tcontinue;\n\t\t\t}\n\t\t\tif (line.includes('validate_each_keys')) {\n\t\t\t\treturn null;\n\t\t\t}\n\t\t\tif (line.includes('svelte/src/internal')) {\n\t\t\t\tcontinue;\n\t\t\t}\n\t\t\tnew_lines.push(line);\n\t\t}\n\n\t\tif (new_lines.length === 1) {\n\t\t\treturn null;\n\t\t}\n\n\t\tdefine_property(error, 'stack', {\n\t\t\tvalue: new_lines.join('\\n')\n\t\t});\n\n\t\tdefine_property(error, 'name', {\n\t\t\t// 'Error' suffix is required for stack traces to be rendered properly\n\t\t\tvalue: `${label}Error`\n\t\t});\n\t}\n\treturn error;\n}\n", "import { hydrating } from '../hydration.js';\nimport { clear_text_content, get_first_child } from '../operations.js';\nimport { queue_micro_task } from '../task.js';\n\n/**\n * @param {HTMLElement} dom\n * @param {boolean} value\n * @returns {void}\n */\nexport function autofocus(dom, value) {\n\tif (value) {\n\t\tconst body = document.body;\n\t\tdom.autofocus = true;\n\n\t\tqueue_micro_task(() => {\n\t\t\tif (document.activeElement === body) {\n\t\t\t\tdom.focus();\n\t\t\t}\n\t\t});\n\t}\n}\n\n/**\n * The child of a textarea actually corresponds to the defaultValue property, so we need\n * to remove it upon hydration to avoid a bug when someone resets the form value.\n * @param {HTMLTextAreaElement} dom\n * @returns {void}\n */\nexport function remove_textarea_child(dom) {\n\tif (hydrating && get_first_child(dom) !== null) {\n\t\tclear_text_content(dom);\n\t}\n}\n\nlet listening_to_form_reset = false;\n\nexport function add_form_reset_listener() {\n\tif (!listening_to_form_reset) {\n\t\tlistening_to_form_reset = true;\n\t\tdocument.addEventListener(\n\t\t\t'reset',\n\t\t\t(evt) => {\n\t\t\t\t// Needs to happen one tick later or else the dom properties of the form\n\t\t\t\t// elements have not updated to their reset values yet\n\t\t\t\tPromise.resolve().then(() => {\n\t\t\t\t\tif (!evt.defaultPrevented) {\n\t\t\t\t\t\tfor (const e of /**@type {HTMLFormElement} */ (evt.target).elements) {\n\t\t\t\t\t\t\t// @ts-expect-error\n\t\t\t\t\t\t\te.__on_r?.();\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\t// In the capture phase to guarantee we get noticed of it (no possiblity of stopPropagation)\n\t\t\t{ capture: true }\n\t\t);\n\t}\n}\n", "import { teardown } from '../../../reactivity/effects.js';\nimport {\n\tactive_effect,\n\tactive_reaction,\n\tset_active_effect,\n\tset_active_reaction\n} from '../../../runtime.js';\nimport { add_form_reset_listener } from '../misc.js';\n\n/**\n * Fires the handler once immediately (unless corresponding arg is set to `false`),\n * then listens to the given events until the render effect context is destroyed\n * @param {EventTarget} target\n * @param {Array<string>} events\n * @param {(event?: Event) => void} handler\n * @param {any} call_handler_immediately\n */\nexport function listen(target, events, handler, call_handler_immediately = true) {\n\tif (call_handler_immediately) {\n\t\thandler();\n\t}\n\n\tfor (var name of events) {\n\t\ttarget.addEventListener(name, handler);\n\t}\n\n\tteardown(() => {\n\t\tfor (var name of events) {\n\t\t\ttarget.removeEventListener(name, handler);\n\t\t}\n\t});\n}\n\n/**\n * @template T\n * @param {() => T} fn\n */\nexport function without_reactive_context(fn) {\n\tvar previous_reaction = active_reaction;\n\tvar previous_effect = active_effect;\n\tset_active_reaction(null);\n\tset_active_effect(null);\n\ttry {\n\t\treturn fn();\n\t} finally {\n\t\tset_active_reaction(previous_reaction);\n\t\tset_active_effect(previous_effect);\n\t}\n}\n\n/**\n * Listen to the given event, and then instantiate a global form reset listener if not already done,\n * to notify all bindings when the form is reset\n * @param {HTMLElement} element\n * @param {string} event\n * @param {(is_reset?: true) => void} handler\n * @param {(is_reset?: true) => void} [on_reset]\n */\nexport function listen_to_event_and_reset_event(element, event, handler, on_reset = handler) {\n\telement.addEventListener(event, () => without_reactive_context(handler));\n\t// @ts-expect-error\n\tconst prev = element.__on_r;\n\tif (prev) {\n\t\t// special case for checkbox that can have multiple binds (group & checked)\n\t\t// @ts-expect-error\n\t\telement.__on_r = () => {\n\t\t\tprev();\n\t\t\ton_reset(true);\n\t\t};\n\t} else {\n\t\t// @ts-expect-error\n\t\telement.__on_r = () => on_reset(true);\n\t}\n\n\tadd_form_reset_listener();\n}\n", "/** @import { Location } from 'locate-character' */\nimport { teardown } from '../../reactivity/effects.js';\nimport { define_property, is_array } from '../../../shared/utils.js';\nimport { hydrating } from '../hydration.js';\nimport { queue_micro_task } from '../task.js';\nimport { FILENAME } from '../../../../constants.js';\nimport * as w from '../../warnings.js';\nimport {\n\tactive_effect,\n\tactive_reaction,\n\tset_active_effect,\n\tset_active_reaction\n} from '../../runtime.js';\nimport { without_reactive_context } from './bindings/shared.js';\n\n/** @type {Set<string>} */\nexport const all_registered_events = new Set();\n\n/** @type {Set<(events: Array<string>) => void>} */\nexport const root_event_handles = new Set();\n\n/**\n * SSR adds onload and onerror attributes to catch those events before the hydration.\n * This function detects those cases, removes the attributes and replays the events.\n * @param {HTMLElement} dom\n */\nexport function replay_events(dom) {\n\tif (!hydrating) return;\n\n\tif (dom.onload) {\n\t\tdom.removeAttribute('onload');\n\t}\n\tif (dom.onerror) {\n\t\tdom.removeAttribute('onerror');\n\t}\n\t// @ts-expect-error\n\tconst event = dom.__e;\n\tif (event !== undefined) {\n\t\t// @ts-expect-error\n\t\tdom.__e = undefined;\n\t\tqueueMicrotask(() => {\n\t\t\tif (dom.isConnected) {\n\t\t\t\tdom.dispatchEvent(event);\n\t\t\t}\n\t\t});\n\t}\n}\n\n/**\n * @param {string} event_name\n * @param {EventTarget} dom\n * @param {EventListener} handler\n * @param {AddEventListenerOptions} options\n */\nexport function create_event(event_name, dom, handler, options) {\n\t/**\n\t * @this {EventTarget}\n\t */\n\tfunction target_handler(/** @type {Event} */ event) {\n\t\tif (!options.capture) {\n\t\t\t// Only call in the bubble phase, else delegated events would be called before the capturing events\n\t\t\thandle_event_propagation.call(dom, event);\n\t\t}\n\t\tif (!event.cancelBubble) {\n\t\t\treturn without_reactive_context(() => {\n\t\t\t\treturn handler.call(this, event);\n\t\t\t});\n\t\t}\n\t}\n\n\t// Chrome has a bug where pointer events don't work when attached to a DOM element that has been cloned\n\t// with cloneNode() and the DOM element is disconnected from the document. To ensure the event works, we\n\t// defer the attachment till after it's been appended to the document. TODO: remove this once Chrome fixes\n\t// this bug. The same applies to wheel events and touch events.\n\tif (\n\t\tevent_name.startsWith('pointer') ||\n\t\tevent_name.startsWith('touch') ||\n\t\tevent_name === 'wheel'\n\t) {\n\t\tqueue_micro_task(() => {\n\t\t\tdom.addEventListener(event_name, target_handler, options);\n\t\t});\n\t} else {\n\t\tdom.addEventListener(event_name, target_handler, options);\n\t}\n\n\treturn target_handler;\n}\n\n/**\n * Attaches an event handler to an element and returns a function that removes the handler. Using this\n * rather than `addEventListener` will preserve the correct order relative to handlers added declaratively\n * (with attributes like `onclick`), which use event delegation for performance reasons\n *\n * @param {EventTarget} element\n * @param {string} type\n * @param {EventListener} handler\n * @param {AddEventListenerOptions} [options]\n */\nexport function on(element, type, handler, options = {}) {\n\tvar target_handler = create_event(type, element, handler, options);\n\n\treturn () => {\n\t\telement.removeEventListener(type, target_handler, options);\n\t};\n}\n\n/**\n * @param {string} event_name\n * @param {Element} dom\n * @param {EventListener} handler\n * @param {boolean} capture\n * @param {boolean} [passive]\n * @returns {void}\n */\nexport function event(event_name, dom, handler, capture, passive) {\n\tvar options = { capture, passive };\n\tvar target_handler = create_event(event_name, dom, handler, options);\n\n\t// @ts-ignore\n\tif (dom === document.body || dom === window || dom === document) {\n\t\tteardown(() => {\n\t\t\tdom.removeEventListener(event_name, target_handler, options);\n\t\t});\n\t}\n}\n\n/**\n * @param {Array<string>} events\n * @returns {void}\n */\nexport function delegate(events) {\n\tfor (var i = 0; i < events.length; i++) {\n\t\tall_registered_events.add(events[i]);\n\t}\n\n\tfor (var fn of root_event_handles) {\n\t\tfn(events);\n\t}\n}\n\n/**\n * @this {EventTarget}\n * @param {Event} event\n * @returns {void}\n */\nexport function handle_event_propagation(event) {\n\tvar handler_element = this;\n\tvar owner_document = /** @type {Node} */ (handler_element).ownerDocument;\n\tvar event_name = event.type;\n\tvar path = event.composedPath?.() || [];\n\tvar current_target = /** @type {null | Element} */ (path[0] || event.target);\n\n\t// composedPath contains list of nodes the event has propagated through.\n\t// We check __root to skip all nodes below it in case this is a\n\t// parent of the __root node, which indicates that there's nested\n\t// mounted apps. In this case we don't want to trigger events multiple times.\n\tvar path_idx = 0;\n\n\t// @ts-expect-error is added below\n\tvar handled_at = event.__root;\n\n\tif (handled_at) {\n\t\tvar at_idx = path.indexOf(handled_at);\n\t\tif (\n\t\t\tat_idx !== -1 &&\n\t\t\t(handler_element === document || handler_element === /** @type {any} */ (window))\n\t\t) {\n\t\t\t// This is the fallback document listener or a window listener, but the event was already handled\n\t\t\t// -> ignore, but set handle_at to document/window so that we're resetting the event\n\t\t\t// chain in case someone manually dispatches the same event object again.\n\t\t\t// @ts-expect-error\n\t\t\tevent.__root = handler_element;\n\t\t\treturn;\n\t\t}\n\n\t\t// We're deliberately not skipping if the index is higher, because\n\t\t// someone could create an event programmatically and emit it multiple times,\n\t\t// in which case we want to handle the whole propagation chain properly each time.\n\t\t// (this will only be a false negative if the event is dispatched multiple times and\n\t\t// the fallback document listener isn't reached in between, but that's super rare)\n\t\tvar handler_idx = path.indexOf(handler_element);\n\t\tif (handler_idx === -1) {\n\t\t\t// handle_idx can theoretically be -1 (happened in some JSDOM testing scenarios with an event listener on the window object)\n\t\t\t// so guard against that, too, and assume that everything was handled at this point.\n\t\t\treturn;\n\t\t}\n\n\t\tif (at_idx <= handler_idx) {\n\t\t\tpath_idx = at_idx;\n\t\t}\n\t}\n\n\tcurrent_target = /** @type {Element} */ (path[path_idx] || event.target);\n\t// there can only be one delegated event per element, and we either already handled the current target,\n\t// or this is the very first target in the chain which has a non-delegated listener, in which case it's safe\n\t// to handle a possible delegated event on it later (through the root delegation listener for example).\n\tif (current_target === handler_element) return;\n\n\t// Proxy currentTarget to correct target\n\tdefine_property(event, 'currentTarget', {\n\t\tconfigurable: true,\n\t\tget() {\n\t\t\treturn current_target || owner_document;\n\t\t}\n\t});\n\n\t// This started because of Chromium issue https://chromestatus.com/feature/5128696823545856,\n\t// where removal or moving of of the DOM can cause sync `blur` events to fire, which can cause logic\n\t// to run inside the current `active_reaction`, which isn't what we want at all. However, on reflection,\n\t// it's probably best that all event handled by Svelte have this behaviour, as we don't really want\n\t// an event handler to run in the context of another reaction or effect.\n\tvar previous_reaction = active_reaction;\n\tvar previous_effect = active_effect;\n\tset_active_reaction(null);\n\tset_active_effect(null);\n\n\ttry {\n\t\t/**\n\t\t * @type {unknown}\n\t\t */\n\t\tvar throw_error;\n\t\t/**\n\t\t * @type {unknown[]}\n\t\t */\n\t\tvar other_errors = [];\n\n\t\twhile (current_target !== null) {\n\t\t\t/** @type {null | Element} */\n\t\t\tvar parent_element =\n\t\t\t\tcurrent_target.assignedSlot ||\n\t\t\t\tcurrent_target.parentNode ||\n\t\t\t\t/** @type {any} */ (current_target).host ||\n\t\t\t\tnull;\n\n\t\t\ttry {\n\t\t\t\t// @ts-expect-error\n\t\t\t\tvar delegated = current_target['__' + event_name];\n\n\t\t\t\tif (delegated !== undefined && !(/** @type {any} */ (current_target).disabled)) {\n\t\t\t\t\tif (is_array(delegated)) {\n\t\t\t\t\t\tvar [fn, ...data] = delegated;\n\t\t\t\t\t\tfn.apply(current_target, [event, ...data]);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tdelegated.call(current_target, event);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tif (throw_error) {\n\t\t\t\t\tother_errors.push(error);\n\t\t\t\t} else {\n\t\t\t\t\tthrow_error = error;\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (event.cancelBubble || parent_element === handler_element || parent_element === null) {\n\t\t\t\tbreak;\n\t\t\t}\n\t\t\tcurrent_target = parent_element;\n\t\t}\n\n\t\tif (throw_error) {\n\t\t\tfor (let error of other_errors) {\n\t\t\t\t// Throw the rest of the errors, one-by-one on a microtask\n\t\t\t\tqueueMicrotask(() => {\n\t\t\t\t\tthrow error;\n\t\t\t\t});\n\t\t\t}\n\t\t\tthrow throw_error;\n\t\t}\n\t} finally {\n\t\t// @ts-expect-error is used above\n\t\tevent.__root = handler_element;\n\t\t// @ts-ignore remove proxy on currentTarget\n\t\tdelete event.currentTarget;\n\t\tset_active_reaction(previous_reaction);\n\t\tset_active_effect(previous_effect);\n\t}\n}\n\n/**\n * In dev, warn if an event handler is not a function, as it means the\n * user probably called the handler or forgot to add a `() =>`\n * @param {() => (event: Event, ...args: any) => void} thunk\n * @param {EventTarget} element\n * @param {[Event, ...any]} args\n * @param {any} component\n * @param {[number, number]} [loc]\n * @param {boolean} [remove_parens]\n */\nexport function apply(\n\tthunk,\n\telement,\n\targs,\n\tcomponent,\n\tloc,\n\thas_side_effects = false,\n\tremove_parens = false\n) {\n\tlet handler;\n\tlet error;\n\n\ttry {\n\t\thandler = thunk();\n\t} catch (e) {\n\t\terror = e;\n\t}\n\n\tif (typeof handler === 'function') {\n\t\thandler.apply(element, args);\n\t} else if (has_side_effects || handler != null || error) {\n\t\tconst filename = component?.[FILENAME];\n\t\tconst location = loc ? ` at ${filename}:${loc[0]}:${loc[1]}` : ` in ${filename}`;\n\n\t\tconst event_name = args[0].type;\n\t\tconst description = `\\`${event_name}\\` handler${location}`;\n\t\tconst suggestion = remove_parens ? 'remove the trailing `()`' : 'add a leading `() =>`';\n\n\t\tw.event_handler_invalid(description, suggestion);\n\n\t\tif (error) {\n\t\t\tthrow error;\n\t\t}\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;AAEO,IAAI,WAAW,MAAM;AACrB,IAAI,WAAW,MAAM,UAAU;AAC/B,IAAI,aAAa,MAAM;AACvB,IAAI,cAAc,OAAO;AACzB,IAAI,kBAAkB,OAAO;AAC7B,IAAI,iBAAiB,OAAO;AAC5B,IAAI,kBAAkB,OAAO;AAC7B,IAAI,mBAAmB,OAAO;AAC9B,IAAI,kBAAkB,MAAM;AAC5B,IAAI,mBAAmB,OAAO;AAM9B,SAAS,YAAY,OAAO;AAClC,SAAO,OAAO,UAAU;AACzB;AAEO,IAAM,OAAO,MAAM;AAAC;AAUpB,SAAS,WAAW,OAAO;AACjC,SAAO,QAAO,+BAAO,UAAS;AAC/B;AAGO,SAAS,IAAI,IAAI;AACvB,SAAO,GAAG;AACX;AAGO,SAAS,QAAQ,KAAK;AAC5B,WAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACpC,QAAI,CAAC,EAAE;AAAA,EACR;AACD;AAMO,SAAS,WAAW;AAE1B,MAAI;AAGJ,MAAI;AAGJ,MAAI,UAAU,IAAI,QAAQ,CAAC,KAAK,QAAQ;AACvC,cAAU;AACV,aAAS;AAAA,EACV,CAAC;AAGD,SAAO,EAAE,SAAS,SAAS,OAAO;AACnC;AASO,SAAS,SAAS,OAAOA,WAAU,OAAO,OAAO;AACvD,SAAO,UAAU,SACd;AAAA;AAAA,IACyBA,UAAU;AAAA;AAAA;AAAA,IAChBA;AAAA,MACnB;AACJ;;;ACjFO,IAAM,UAAU,KAAK;AACrB,IAAM,SAAS,KAAK;AACpB,IAAM,gBAAgB,KAAK;AAC3B,IAAM,eAAe,KAAK;AAC1B,IAAM,gBAAgB,KAAK;AAC3B,IAAM,cAAc,KAAK;AACzB,IAAM,kBAAkB,KAAK;AAC7B,IAAM,UAAU,KAAK;AACrB,IAAM,eAAe,KAAK;AAC1B,IAAM,QAAQ,KAAK;AACnB,IAAM,QAAQ,KAAK;AACnB,IAAM,cAAc,KAAK;AACzB,IAAM,QAAQ,KAAK;AACnB,IAAM,YAAY,KAAK;AACvB,IAAM,aAAa,KAAK;AAExB,IAAM,qBAAqB,KAAK;AAEhC,IAAM,sBAAsB,KAAK;AACjC,IAAM,iBAAiB,KAAK;AAC5B,IAAM,cAAc,KAAK;AACzB,IAAM,qBAAqB,KAAK;AAEhC,IAAM,eAAe,OAAO,QAAQ;AACpC,IAAM,wBAAwB,OAAO,iBAAiB;AACtD,IAAM,eAAe,OAAO,cAAc;AAC1C,IAAM,sBAAsB,OAAO,EAAE;;;AClBrC,SAAS,8BAA8B;AAC7C,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA;AAAA,iDAAiL;AAEzM,UAAM,OAAO;AACb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,kDAAkD;AAAA,EACnE;AACD;AA6CO,SAAS,sBAAsB,QAAQ,QAAQ,WAAW;AAChE,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA,EAA0B,MAAM,aAAa,MAAM,wBAAwB,SAAS;AAAA,2CAAoF;AAEhM,UAAM,OAAO;AACb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,4CAA4C;AAAA,EAC7D;AACD;AAQO,SAAS,0BAA0B,WAAW,MAAM;AAC1D,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA,2BAAuD,SAAS,eAAe,IAAI;AAAA,+CAA4N;AAEvU,UAAM,OAAO;AACb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,gDAAgD;AAAA,EACjE;AACD;AAMO,SAAS,0BAA0B;AACzC,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA;AAAA,6CAA4H;AAEpJ,UAAM,OAAO;AACb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,8CAA8C;AAAA,EAC/D;AACD;AASO,SAAS,mBAAmB,GAAG,GAAG,OAAO;AAC/C,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA,EAAuB,QAAQ,wCAAwC,KAAK,iBAAiB,CAAC,QAAQ,CAAC,KAAK,iDAAiD,CAAC,QAAQ,CAAC,EAAE;AAAA,wCAA2C;AAE5O,UAAM,OAAO;AACb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,yCAAyC;AAAA,EAC1D;AACD;AAOO,SAAS,mBAAmB,MAAM;AACxC,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA,IAAyB,IAAI;AAAA,wCAA8F;AAEnJ,UAAM,OAAO;AACb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,yCAAyC;AAAA,EAC1D;AACD;AAMO,SAAS,4BAA4B;AAC3C,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA;AAAA,+CAA8K;AAEtM,UAAM,OAAO;AACb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,gDAAgD;AAAA,EACjE;AACD;AAOO,SAAS,cAAc,MAAM;AACnC,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA,IAAoB,IAAI;AAAA,mCAAiH;AAEjK,UAAM,OAAO;AACb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,oCAAoC;AAAA,EACrD;AACD;AAMO,SAAS,+BAA+B;AAC9C,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA;AAAA,kDAAmQ;AAE3R,UAAM,OAAO;AACb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,mDAAmD;AAAA,EACpE;AACD;AAMO,SAAS,mBAAmB;AAClC,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA;AAAA,sCAA4F;AAEpH,UAAM,OAAO;AACb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,uCAAuC;AAAA,EACxD;AACD;AAMO,SAAS,kBAAkB;AACjC,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA;AAAA,qCAA0M;AAElO,UAAM,OAAO;AACb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,sCAAsC;AAAA,EACvD;AACD;AAOO,SAAS,sBAAsB,MAAM;AAC3C,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA,IAA4B,IAAI;AAAA,2CAAkF;AAE1I,UAAM,OAAO;AACb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,4CAA4C;AAAA,EAC7D;AACD;AAOO,SAAS,oBAAoB,KAAK;AACxC,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA,mBAAyC,GAAG,yBAAyB,GAAG;AAAA,yCAAmE;AAEnK,UAAM,OAAO;AACb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,0CAA0C;AAAA,EAC3D;AACD;AAOO,SAAS,oBAAoB,UAAU;AAC7C,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA,oDAA0E,QAAQ;AAAA,yCAA2D;AAErK,UAAM,OAAO;AACb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,0CAA0C;AAAA,EAC3D;AACD;AAOO,SAAS,oBAAoB,MAAM;AACzC,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA,QAA8B,IAAI;AAAA,yCAAoH;AAE9K,UAAM,OAAO;AACb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,0CAA0C;AAAA,EAC3D;AACD;AAMO,SAAS,0BAA0B;AACzC,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA;AAAA,6CAAmN;AAE3O,UAAM,OAAO;AACb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,8CAA8C;AAAA,EAC/D;AACD;AAMO,SAAS,wBAAwB;AACvC,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA;AAAA,2CAA8G;AAEtI,UAAM,OAAO;AACb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,4CAA4C;AAAA,EAC7D;AACD;AAMO,SAAS,0BAA0B;AACzC,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA;AAAA,6CAAsM;AAE9N,UAAM,OAAO;AACb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,8CAA8C;AAAA,EAC/D;AACD;AAMO,SAAS,wBAAwB;AACvC,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA;AAAA,2CAA8M;AAEtO,UAAM,OAAO;AACb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,4CAA4C;AAAA,EAC7D;AACD;;;ACjVO,IAAM,qBAAqB;AAC3B,IAAM,sBAAsB,KAAK;AAEjC,IAAM,qBAAqB,KAAK;AAChC,IAAM,mBAAmB,KAAK;AAC9B,IAAM,sBAAsB,KAAK;AAEjC,IAAM,qBAAqB;AAC3B,IAAM,iBAAiB,KAAK;AAC5B,IAAM,mBAAmB,KAAK;AAC9B,IAAM,oBAAoB,KAAK;AAC/B,IAAM,wBAAwB,KAAK;AAEnC,IAAM,gBAAgB;AACtB,IAAM,iBAAiB,KAAK;AAC5B,IAAM,oBAAoB,KAAK;AAE/B,IAAM,oBAAoB;AAC1B,IAAM,2BAA2B,KAAK;AAEtC,IAAM,kBAAkB;AAExB,IAAM,uBAAuB;AAC7B,IAAM,gBAAgB;AACtB,IAAM,kBAAkB,CAAC;AAGzB,IAAM,kCAAkC,KAAK;AAE7C,IAAM,gBAAgB,OAAO;AAG7B,IAAM,WAAW,OAAO,UAAU;AAClC,IAAM,MAAM,OAAO,KAAK;AAExB,IAAM,gBAAgB;;;AC/B7B,IAAI,OAAO;AACX,IAAI,SAAS;AAMN,SAAS,6BAA6B,KAAK;AACjD,MAAI,cAAK;AACR,YAAQ,KAAK;AAAA,4BAAsE,GAAG;AAAA,oDAAsG,MAAM,MAAM;AAAA,EACzM,OAAO;AACN,YAAQ,KAAK,mDAAmD;AAAA,EACjE;AACD;AAQO,SAAS,2BAA2B,YAAY;AACtD,MAAI,cAAK;AACR,YAAQ,KAAK;AAAA,IAA4C,aACtD;AAAA;AAAA,EAEH,UAAU,KACP,iFAAiF;AAAA,kDAAqD,MAAM,MAAM;AAAA,EACtJ,OAAO;AACN,YAAQ,KAAK,iDAAiD;AAAA,EAC/D;AACD;;;ACxBA,IAAM,QAAQ,CAAC;AAQR,SAAS,SAAS,OAAO,eAAe,OAAO;AACrD,MAAI,gBAAO,CAAC,cAAc;AAEzB,UAAM,QAAQ,CAAC;AAEf,UAAM,OAAO,MAAM,OAAO,oBAAI,IAAI,GAAG,IAAI,KAAK;AAC9C,QAAI,MAAM,WAAW,KAAK,MAAM,CAAC,MAAM,IAAI;AAE1C,MAAE,2BAA2B;AAAA,IAC9B,WAAW,MAAM,SAAS,GAAG;AAE5B,YAAM,QAAQ,MAAM,SAAS,KAAK,MAAM,MAAM,GAAG,CAAC,IAAI,MAAM,MAAM,GAAG,EAAE;AACvE,YAAM,SAAS,MAAM,SAAS,MAAM;AAEpC,UAAI,WAAW,MAAM,IAAI,CAAC,SAAS,YAAY,IAAI,EAAE,EAAE,KAAK,IAAI;AAChE,UAAI,SAAS,EAAG,aAAY;AAAA,WAAc,MAAM;AAEhD,MAAE,2BAA2B,QAAQ;AAAA,IACtC;AAEA,WAAO;AAAA,EACR;AAEA,SAAO,MAAM,OAAO,oBAAI,IAAI,GAAG,IAAI,KAAK;AACzC;AAWA,SAAS,MAAM,OAAO,QAAQ,MAAM,OAAO,WAAW,MAAM;AAC3D,MAAI,OAAO,UAAU,YAAY,UAAU,MAAM;AAChD,QAAI,YAAY,OAAO,IAAI,KAAK;AAChC,QAAI,cAAc,OAAW,QAAO;AAEpC,QAAI,iBAAiB,IAAK;AAAA;AAAA,MAAmC,IAAI,IAAI,KAAK;AAAA;AAC1E,QAAI,iBAAiB,IAAK;AAAA;AAAA,MAAmC,IAAI,IAAI,KAAK;AAAA;AAE1E,QAAI,SAAS,KAAK,GAAG;AACpB,UAAI;AAAA;AAAA,QAAqC,MAAM,MAAM,MAAM;AAAA;AAC3D,aAAO,IAAI,OAAO,IAAI;AAEtB,UAAI,aAAa,MAAM;AACtB,eAAO,IAAI,UAAU,IAAI;AAAA,MAC1B;AAEA,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK,GAAG;AACzC,YAAI,UAAU,MAAM,CAAC;AACrB,YAAI,KAAK,OAAO;AACf,eAAK,CAAC,IAAI,MAAM,SAAS,QAAQ,eAAM,GAAG,IAAI,IAAI,CAAC,MAAM,MAAM,KAAK;AAAA,QACrE;AAAA,MACD;AAEA,aAAO;AAAA,IACR;AAEA,QAAI,iBAAiB,KAAK,MAAM,kBAAkB;AAEjD,aAAO,CAAC;AACR,aAAO,IAAI,OAAO,IAAI;AAEtB,UAAI,aAAa,MAAM;AACtB,eAAO,IAAI,UAAU,IAAI;AAAA,MAC1B;AAEA,eAAS,OAAO,OAAO;AAEtB,aAAK,GAAG,IAAI,MAAM,MAAM,GAAG,GAAG,QAAQ,eAAM,GAAG,IAAI,IAAI,GAAG,KAAK,MAAM,KAAK;AAAA,MAC3E;AAEA,aAAO;AAAA,IACR;AAEA,QAAI,iBAAiB,MAAM;AAC1B;AAAA;AAAA,QAAmC,gBAAgB,KAAK;AAAA;AAAA,IACzD;AAEA,QAAI;AAAA,IAA8C,MAAO,WAAY,YAAY;AAChF,aAAO;AAAA;AAAA,QACiC,MAAO,OAAO;AAAA,QACrD;AAAA,QACA,eAAM,GAAG,IAAI,cAAc;AAAA,QAC3B;AAAA;AAAA,QAEA;AAAA,MACD;AAAA,IACD;AAAA,EACD;AAEA,MAAI,iBAAiB,aAAa;AAEjC;AAAA;AAAA,MAAmC;AAAA;AAAA,EACpC;AAEA,MAAI;AACH;AAAA;AAAA,MAAmC,gBAAgB,KAAK;AAAA;AAAA,EACzD,SAAS,GAAG;AACX,QAAI,cAAK;AACR,YAAM,KAAK,IAAI;AAAA,IAChB;AAEA;AAAA;AAAA,MAAmC;AAAA;AAAA,EACpC;AACD;;;AC5HO,IAAM,wBACZ,OAAO,wBAAwB,cAC5B,CAA2B,OAAO,WAAW,IAAI,CAAC,IAClD;AAEJ,IAAI,uBAAuB;AAC3B,IAAI,sBAAsB;AAG1B,IAAI,6BAA6B,CAAC;AAElC,IAAI,4BAA4B,CAAC;AAEjC,SAAS,sBAAsB;AAC9B,yBAAuB;AACvB,QAAM,QAAQ,2BAA2B,MAAM;AAC/C,+BAA6B,CAAC;AAC9B,UAAQ,KAAK;AACd;AAEA,SAAS,qBAAqB;AAC7B,wBAAsB;AACtB,QAAM,QAAQ,0BAA0B,MAAM;AAC9C,8BAA4B,CAAC;AAC7B,UAAQ,KAAK;AACd;AAKO,SAAS,iBAAiB,IAAI;AACpC,MAAI,CAAC,sBAAsB;AAC1B,2BAAuB;AACvB,mBAAe,mBAAmB;AAAA,EACnC;AACA,6BAA2B,KAAK,EAAE;AACnC;AAKO,SAAS,gBAAgB,IAAI;AACnC,MAAI,CAAC,qBAAqB;AACzB,0BAAsB;AACtB,0BAAsB,kBAAkB;AAAA,EACzC;AACA,4BAA0B,KAAK,EAAE;AAClC;AAKO,SAAS,cAAc;AAC7B,MAAI,sBAAsB;AACzB,wBAAoB;AAAA,EACrB;AACA,MAAI,qBAAqB;AACxB,uBAAmB;AAAA,EACpB;AACD;;;ACnDA,IAAM,aAAa,CAAC;AAEpB,IAAM,iBAAiB;AACvB,IAAM,kBAAkB;AAExB,SAAS,YAAY;AACpB,QAAMC,SAAQ,IAAI,MAAM,EAAE;AAC1B,MAAI,CAACA,OAAO,QAAO;AAEnB,QAAM,UAAU,CAAC;AAEjB,aAAW,QAAQA,OAAM,MAAM,IAAI,GAAG;AACrC,QAAI,QAAQ,eAAe,KAAK,IAAI,KAAK,gBAAgB,KAAK,IAAI;AAElE,QAAI,OAAO;AACV,cAAQ,KAAK;AAAA,QACZ,MAAM,MAAM,CAAC;AAAA,QACb,MAAM,CAAC,MAAM,CAAC;AAAA,QACd,QAAQ,CAAC,MAAM,CAAC;AAAA,MACjB,CAAC;AAAA,IACF;AAAA,EACD;AAEA,SAAO;AACR;AAMO,SAAS,gBAAgB;AAzChC;AA2CC,QAAMA,UAAQ,eAAU,MAAV,mBAAa,MAAM;AACjC,MAAI,CAACA,OAAO,QAAO;AAEnB,WAAS,IAAI,GAAG,IAAIA,OAAM,QAAQ,KAAK;AACtC,UAAM,QAAQA,OAAM,CAAC;AACrB,UAAM,UAAU,WAAW,MAAM,IAAI;AACrC,QAAI,CAAC,SAAS;AAOb,UAAI,MAAM,EAAG,QAAO;AACpB;AAAA,IACD;AAEA,eAAW,UAAU,SAAS;AAC7B,UAAI,OAAO,OAAO,MAAM;AACvB,eAAO;AAAA,MACR;AACA,UAAI,OAAO,MAAM,OAAO,MAAM,QAAQ,OAAO,IAAI,OAAO,MAAM,MAAM;AACnE,eAAO,OAAO;AAAA,MACf;AAAA,IACD;AAAA,EACD;AAEA,SAAO;AACR;AAEO,IAAM,YAAY,OAAO,WAAW;AAOpC,SAAS,oBAAoB;AAhFpC;AAiFC,QAAM,SAAQ,eAAU,MAAV,mBAAc;AAE5B,MAAI,OAAO;AACV,KAAC,gBAAW,MAAM,UAAjB,iBAA2B,CAAC,IAAG,KAAK;AAAA,MACpC;AAAA;AAAA,MAEA,KAAK;AAAA;AAAA,MAEL,WAAW;AAAA,IACZ,CAAC;AAAA,EACF;AACD;AAKO,SAAS,gBAAgB,WAAW;AAjG3C;AAkGC,QAAM,OAAM,eAAU,MAAV,mBAAc;AAE1B,MAAI,KAAK;AACR,UAAM,kBAAkB,WAAW,IAAI,IAAI;AAC3C,UAAM,WAAW,gBAAgB,gBAAgB,SAAS,CAAC;AAE3D,aAAS,MAAM;AACf,aAAS,YAAY;AAAA,EACtB;AACD;AAQO,SAAS,UAAU,QAAQ,OAAO,SAAS,OAAO,eAAe,OAAO;AAC9E,MAAI,UAAU,CAAC,QAAQ;AACtB,UAAM,YAAY;AAClB,UAAM,WAAW,OAAO,qBAAqB;AAC7C,QAAI,YAAY,CAAC,UAAU,UAAU,SAAS,GAAG;AAChD,UAAI,WAAW,UAAU,QAAQ;AAEjC,UAAI,MAAM,QAAQ,MAAM,UAAU,QAAQ,KAAK,CAAC,cAAc;AAC7D,QAAE,0BAA0B,UAAU,QAAQ,GAAG,MAAM,QAAQ,GAAG,SAAS,QAAQ,CAAC;AAAA,MACrF;AAAA,IACD;AAAA,EACD;AAEA,sBAAoB,QAAQ,OAAO,oBAAI,IAAI,CAAC;AAC7C;AAOO,SAAS,iBAAiB,YAAY,WAAW,eAAe,OAAO;AAC7E,kBAAgB,MAAM;AACrB,cAAU,WAAW,GAAG,WAAW,OAAO,YAAY;AAAA,EACvD,CAAC;AACF;AAMO,SAAS,gBAAgB,MAAM,IAAI;AACzC,MAAI,GAAG,WAAW,MAAM;AACvB;AAAA,EACD;AAEA,SAAO,MAAM;AACZ,QAAI,KAAK,WAAW,MAAM;AACzB,SAAG,SAAS;AACZ;AAAA,IACD;AAEA,eAAW,SAAS,KAAK,QAAQ;AAChC,SAAG,OAAO,IAAI,KAAK;AAAA,IACpB;AAEA,WAAO,KAAK;AAAA,EACb;AACD;AAOA,SAAS,oBAAoB,QAAQ,OAAO,MAAM;AACjD,QAAM;AAAA;AAAA,IAAyC,iCAAS;AAAA;AAExD,MAAI,UAAU;AAEb,QAAI,YAAY,YAAY,SAAS,UAAU,MAAM;AACpD,eAAS,OAAO,IAAI,KAAK;AAAA,IAC1B;AAAA,EACD,WAAW,UAAU,OAAO,WAAW,UAAU;AAChD,QAAI,KAAK,IAAI,MAAM,EAAG;AACtB,SAAK,IAAI,MAAM;AACf,QAAI,aAAa,UAAU,OAAO,SAAS,GAAG;AAI7C,oBAAc,MAAM;AACnB,eAAO,SAAS,EAAE,KAAK;AAAA,MACxB,CAAC;AAAA,IACF,OAAO;AACN,UAAI,QAAQ,iBAAiB,MAAM;AAEnC,UAAI,UAAU,OAAO,WAAW;AAE/B,mBAAW,OAAO,QAAQ;AACzB,8BAAoB,OAAO,GAAG,GAAG,OAAO,IAAI;AAAA,QAC7C;AAAA,MACD,WAAW,UAAU,MAAM,WAAW;AAErC,iBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK,GAAG;AAC1C,8BAAoB,OAAO,CAAC,GAAG,OAAO,IAAI;AAAA,QAC3C;AAAA,MACD;AAAA,IACD;AAAA,EACD;AACD;AAOA,SAAS,UAAU,UAAU,WAAW;AACvC,MAAI,SAAS,WAAW,MAAM;AAC7B,WAAO;AAAA,EACR;AAEA,SACC,SAAS,OAAO,IAAI,SAAS,KAC5B,SAAS,WAAW,QAAQ,UAAU,SAAS,QAAQ,SAAS;AAEnE;AAMA,SAAS,UAAU,UAAU;AAlO7B;AAmOC,WACC,0CAAU,WAAV,mBAAkB,SAAS,OAAO,UAClC;AAAA;AAAA,IAAwC,SAAS;AAAA,EAAO;AAE1D;AAEA,IAAI,OAAO;AAKJ,SAAS,0BAA0B,IAAI;AAC7C,SAAO;AACP,KAAG;AACH,SAAO;AACR;AAKO,SAAS,gBAAgB,UAAU;AACzC,MAAI,KAAM;AAEV,QAAM,YAAY,cAAc;AAEhC,MAAI,aAAa,CAAC,UAAU,UAAU,SAAS,GAAG;AACjD,QAAI,WAAW,UAAU,QAAQ;AAGjC,QAAI,SAAS,QAAQ,MAAM,UAAU,QAAQ,GAAG;AAE/C,MAAE,2BAA2B,UAAU,QAAQ,GAAG,SAAS,QAAQ,CAAC;AAAA,IACrE,OAAO;AACN,MAAE,2BAA2B;AAAA,IAC9B;AAAA,EACD;AACD;;;ACrQO,SAAS,OAAO,OAAO;AAC7B,SAAO,UAAU,KAAK;AACvB;AAOO,SAAS,eAAe,GAAG,GAAG;AACpC,SAAO,KAAK,IACT,KAAK,IACL,MAAM,KAAM,MAAM,QAAQ,OAAO,MAAM,YAAa,OAAO,MAAM;AACrE;AAOO,SAAS,UAAU,GAAG,GAAG;AAC/B,SAAO,MAAM;AACd;AAGO,SAAS,YAAY,OAAO;AAClC,SAAO,CAAC,eAAe,OAAO,KAAK,CAAC;AACrC;;;ACQO,IAAI,kBAAkB,oBAAI,IAAI;AAK9B,SAAS,oBAAoB,GAAG;AACtC,oBAAkB;AACnB;AAQO,SAAS,OAAO,GAAGC,QAAO;AAEhC,MAAI,SAAS;AAAA,IACZ,GAAG;AAAA;AAAA,IACH;AAAA,IACA,WAAW;AAAA,IACX;AAAA,IACA,IAAI;AAAA,IACJ,IAAI;AAAA,EACL;AAEA,MAAI,gBAAO,mBAAmB;AAC7B,WAAO,UAAUA,UAASC,WAAU,WAAW;AAC/C,WAAO,QAAQ;AAAA,EAChB;AAEA,SAAO;AACR;AAMO,SAAS,MAAM,GAAG;AACxB,SAAO,oBAAoB,OAAO,CAAC,CAAC;AACrC;AASO,SAAS,eAAe,eAAe,YAAY,OAAO;AAtFjE;AAuFC,QAAM,IAAI,OAAO,aAAa;AAC9B,MAAI,CAAC,WAAW;AACf,MAAE,SAAS;AAAA,EACZ;AAIA,MAAI,oBAAoB,sBAAsB,QAAQ,kBAAkB,MAAM,MAAM;AACnF,MAAC,uBAAkB,GAAE,MAApB,GAAoB,IAAM,CAAC,IAAG,KAAK,CAAC;AAAA,EACtC;AAEA,SAAO;AACR;AAQO,SAAS,cAAc,GAAG,YAAY,OAAO;AACnD,SAAO,oBAAoB,eAAe,GAAG,SAAS,CAAC;AACxD;AAOA,SAAS,oBAAoBC,SAAQ;AACpC,MAAI,oBAAoB,SAAS,gBAAgB,IAAI,aAAa,GAAG;AACpE,QAAI,oBAAoB,MAAM;AAC7B,0BAAoB,CAACA,OAAM,CAAC;AAAA,IAC7B,OAAO;AACN,sBAAgB,KAAKA,OAAM;AAAA,IAC5B;AAAA,EACD;AAEA,SAAOA;AACR;AAOO,SAAS,OAAOA,SAAQ,OAAO;AACrC;AAAA,IACCA;AAAA,IACA,QAAQ,MAAM,IAAIA,OAAM,CAAC;AAAA,EAC1B;AACA,SAAO;AACR;AAQO,SAAS,IAAIA,SAAQ,OAAO;AAClC,MACC,oBAAoB,QACpB,SAAS,MACR,gBAAgB,KAAK,UAAU,mBAAmB;AAAA;AAAA,GAGlD,oBAAoB,QAAQ,CAAC,gBAAgB,SAASA,OAAM,IAC5D;AACD,IAAE,sBAAsB;AAAA,EACzB;AAEA,SAAO,aAAaA,SAAQ,KAAK;AAClC;AAQO,SAAS,aAAaA,SAAQ,OAAO;AAC3C,MAAI,CAACA,QAAO,OAAO,KAAK,GAAG;AAC1B,QAAI,YAAYA,QAAO;AACvB,IAAAA,QAAO,IAAI;AACX,IAAAA,QAAO,KAAK,wBAAwB;AAEpC,QAAI,gBAAO,mBAAmB;AAC7B,MAAAA,QAAO,UAAUD,WAAU,WAAW;AACtC,UAAI,iBAAiB,MAAM;AAC1B,QAAAC,QAAO,sBAAsB;AAC7B,QAAAA,QAAO,YAAPA,QAAO,UAAY;AAAA,MACpB;AAAA,IACD;AAEA,mBAAeA,SAAQ,KAAK;AAM5B,QACC,SAAS,KACT,kBAAkB,SACjB,cAAc,IAAI,WAAW,MAC7B,cAAc,KAAK,gBAAgB,kBAAkB,GACrD;AACD,UAAI,qBAAqB,MAAM;AAC9B,6BAAqB,CAACA,OAAM,CAAC;AAAA,MAC9B,OAAO;AACN,yBAAiB,KAAKA,OAAM;AAAA,MAC7B;AAAA,IACD;AAEA,QAAI,gBAAO,gBAAgB,OAAO,GAAG;AACpC,YAAM,WAAW,MAAM,KAAK,eAAe;AAC3C,UAAI,6BAA6B;AACjC,6BAAuB,IAAI;AAC3B,UAAI;AACH,mBAAWC,WAAU,UAAU;AAG9B,eAAKA,QAAO,IAAI,WAAW,GAAG;AAC7B,8BAAkBA,SAAQ,WAAW;AAAA,UACtC;AACA,cAAI,gBAAgBA,OAAM,GAAG;AAC5B,0BAAcA,OAAM;AAAA,UACrB;AAAA,QACD;AAAA,MACD,UAAE;AACD,+BAAuB,0BAA0B;AAAA,MAClD;AACA,sBAAgB,MAAM;AAAA,IACvB;AAAA,EACD;AAEA,SAAO;AACR;AAOA,SAAS,eAAe,QAAQ,QAAQ;AACvC,MAAI,YAAY,OAAO;AACvB,MAAI,cAAc,KAAM;AAExB,MAAI,QAAQ,SAAS;AACrB,MAAI,SAAS,UAAU;AAEvB,WAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAChC,QAAI,WAAW,UAAU,CAAC;AAC1B,QAAI,QAAQ,SAAS;AAGrB,SAAK,QAAQ,WAAW,EAAG;AAG3B,QAAI,CAAC,SAAS,aAAa,cAAe;AAG1C,QAAI,iBAAQ,QAAQ,oBAAoB,GAAG;AAC1C,sBAAgB,IAAI,QAAQ;AAC5B;AAAA,IACD;AAEA,sBAAkB,UAAU,MAAM;AAGlC,SAAK,SAAS,QAAQ,cAAc,GAAG;AACtC,WAAK,QAAQ,aAAa,GAAG;AAC5B;AAAA;AAAA,UAAuC;AAAA,UAAW;AAAA,QAAW;AAAA,MAC9D,OAAO;AACN;AAAA;AAAA,UAAuC;AAAA,QAAS;AAAA,MACjD;AAAA,IACD;AAAA,EACD;AACD;;;ACtOO,SAAS,QAAQ,IAAI;AAC3B,MAAI,QAAQ,UAAU;AAEtB,MAAI,kBAAkB,MAAM;AAC3B,aAAS;AAAA,EACV,OAAO;AAGN,kBAAc,KAAK;AAAA,EACpB;AAEA,MAAI,iBACH,oBAAoB,SAAS,gBAAgB,IAAI,aAAa;AAAA;AAAA,IACnC;AAAA,MACxB;AAGJ,QAAM,SAAS;AAAA,IACd,UAAU;AAAA,IACV,KAAK;AAAA,IACL,MAAM;AAAA,IACN;AAAA,IACA,GAAG;AAAA,IACH;AAAA,IACA,WAAW;AAAA,IACX,IAAI;AAAA,IACJ;AAAA;AAAA,MAAqB;AAAA;AAAA,IACrB,IAAI;AAAA,IACJ,QAAQ,kBAAkB;AAAA,EAC3B;AAEA,MAAI,gBAAO,mBAAmB;AAC7B,WAAO,UAAUC,WAAU,WAAW;AAAA,EACvC;AAEA,MAAI,mBAAmB,MAAM;AAC5B,KAAC,eAAe,aAAf,eAAe,WAAa,CAAC,IAAG,KAAK,MAAM;AAAA,EAC7C;AAEA,SAAO;AACR;AAQO,SAAS,mBAAmB,IAAI;AACtC,QAAM,SAAS,QAAQ,EAAE;AACzB,SAAO,SAAS;AAChB,SAAO;AACR;AAMA,SAAS,yBAAyBC,UAAS;AAC1C,MAAI,WAAWA,SAAQ;AAEvB,MAAI,aAAa,MAAM;AACtB,IAAAA,SAAQ,WAAW;AAEnB,aAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK,GAAG;AAC5C,UAAIC,SAAQ,SAAS,CAAC;AACtB,WAAKA,OAAM,IAAI,aAAa,GAAG;AAC9B;AAAA;AAAA,UAAwCA;AAAA,QAAM;AAAA,MAC/C,OAAO;AACN;AAAA;AAAA,UAAsCA;AAAA,QAAM;AAAA,MAC7C;AAAA,IACD;AAAA,EACD;AACD;AAOA,IAAI,QAAQ,CAAC;AAMb,SAAS,0BAA0BD,UAAS;AAC3C,MAAI,SAASA,SAAQ;AACrB,SAAO,WAAW,MAAM;AACvB,SAAK,OAAO,IAAI,aAAa,GAAG;AAC/B;AAAA;AAAA,QAA8B;AAAA;AAAA,IAC/B;AACA,aAAS,OAAO;AAAA,EACjB;AACA,SAAO;AACR;AAOO,SAAS,gBAAgBA,UAAS;AACxC,MAAI;AACJ,MAAI,qBAAqB;AAEzB,oBAAkB,0BAA0BA,QAAO,CAAC;AAEpD,MAAI,cAAK;AACR,QAAI,uBAAuB;AAC3B,wBAAoB,oBAAI,IAAI,CAAC;AAC7B,QAAI;AACH,UAAI,MAAM,SAASA,QAAO,GAAG;AAC5B,QAAE,wBAAwB;AAAA,MAC3B;AAEA,YAAM,KAAKA,QAAO;AAElB,+BAAyBA,QAAO;AAChC,cAAQ,gBAAgBA,QAAO;AAAA,IAChC,UAAE;AACD,wBAAkB,kBAAkB;AACpC,0BAAoB,oBAAoB;AACxC,YAAM,IAAI;AAAA,IACX;AAAA,EACD,OAAO;AACN,QAAI;AACH,+BAAyBA,QAAO;AAChC,cAAQ,gBAAgBA,QAAO;AAAA,IAChC,UAAE;AACD,wBAAkB,kBAAkB;AAAA,IACrC;AAAA,EACD;AAEA,SAAO;AACR;AAMO,SAAS,eAAeA,UAAS;AACvC,MAAI,QAAQ,gBAAgBA,QAAO;AACnC,MAAI,UACF,kBAAkBA,SAAQ,IAAI,aAAa,MAAMA,SAAQ,SAAS,OAAO,cAAc;AAEzF,oBAAkBA,UAAS,MAAM;AAEjC,MAAI,CAACA,SAAQ,OAAO,KAAK,GAAG;AAC3B,IAAAA,SAAQ,IAAI;AACZ,IAAAA,SAAQ,KAAK,wBAAwB;AAAA,EACtC;AACD;AAMO,SAAS,gBAAgBA,UAAS;AACxC,2BAAyBA,QAAO;AAChC,mBAAiBA,UAAS,CAAC;AAC3B,oBAAkBA,UAAS,SAAS;AAEpC,EAAAA,SAAQ,IAAIA,SAAQ,WAAWA,SAAQ,OAAOA,SAAQ,MAAMA,SAAQ,YAAY;AACjF;;;AC/LO,SAAS,0BAA0B;AACzC,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA;AAAA,6CAAwM;AAEhO,UAAM,OAAO;AACb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,8CAA8C;AAAA,EAC/D;AACD;AAOO,SAAS,4BAA4B,MAAM;AACjD,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA,IAAkC,IAAI;AAAA,iDAA4G;AAE1K,UAAM,OAAO;AACb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,kDAAkD;AAAA,EACnE;AACD;AAOO,SAAS,oBAAoB,MAAM;AACzC,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA,IAA0B,IAAI;AAAA,yCAAyF;AAE/I,UAAM,OAAO;AACb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,0CAA0C;AAAA,EAC3D;AACD;AAMO,SAAS,oCAAoC;AACnD,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA;AAAA,uDAAmK;AAE3L,UAAM,OAAO;AACb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,wDAAwD;AAAA,EACzE;AACD;;;ACzBA,IAAM,kBAAkB;AACxB,IAAM,aAAa;AAGnB,IAAM,iBAAiB,oBAAI,QAAQ;AAC5B,IAAI,oBAAoB;AAG/B,IAAI,iBAAiB;AAErB,IAAIE,wBAAuB;AAG3B,IAAI,wBAAwB;AAErB,IAAI,qBAAqB;AACzB,IAAI,uBAAuB;AAG3B,SAAS,uBAAuB,OAAO;AAC7C,uBAAqB;AACtB;AAGO,SAAS,yBAAyB,OAAO;AAC/C,yBAAuB;AACxB;AAKA,IAAI,sBAAsB,CAAC;AAE3B,IAAI,cAAc;AAElB,IAAI,mBAAmB,CAAC;AAIjB,IAAI,kBAAkB;AAGtB,SAAS,oBAAoB,UAAU;AAC7C,oBAAkB;AACnB;AAGO,IAAI,gBAAgB;AAGpB,SAAS,kBAAkBC,SAAQ;AACzC,kBAAgBA;AACjB;AAOO,IAAI,kBAAkB;AAKtB,SAAS,oBAAoB,SAAS;AAC5C,oBAAkB;AACnB;AAQO,IAAI,WAAW;AAEtB,IAAI,eAAe;AAOZ,IAAI,mBAAmB;AAGvB,SAAS,qBAAqB,OAAO;AAC3C,qBAAmB;AACpB;AAMA,IAAI,gBAAgB;AAGpB,IAAI,eAAe;AAIZ,IAAI,gBAAgB;AAGpB,IAAI,mBAAmB;AAGvB,SAAS,qBAAqB,OAAO;AAC3C,qBAAmB;AACpB;AAIO,IAAI,oBAAoB;AAGxB,SAAS,sBAAsB,SAAS;AAC9C,sBAAoB;AACrB;AAYO,IAAI,iCAAiC;AAGrC,SAAS,mCAAmC,IAAI;AACtD,mCAAiC;AAClC;AAEO,SAAS,0BAA0B;AACzC,SAAO,EAAE;AACV;AAGO,SAAS,WAAW;AAC1B,SAAO,CAAC,oBAAqB,sBAAsB,QAAQ,kBAAkB,MAAM;AACpF;AAQO,SAAS,gBAAgB,UAAU;AA/L1C;AAgMC,MAAI,QAAQ,SAAS;AAErB,OAAK,QAAQ,WAAW,GAAG;AAC1B,WAAO;AAAA,EACR;AAEA,OAAK,QAAQ,iBAAiB,GAAG;AAChC,QAAI,eAAe,SAAS;AAC5B,QAAI,cAAc,QAAQ,aAAa;AAEvC,QAAI,iBAAiB,MAAM;AAC1B,UAAI;AACJ,UAAI;AACJ,UAAI,mBAAmB,QAAQ,kBAAkB;AACjD,UAAI,uBAAuB,cAAc,kBAAkB,QAAQ,CAAC;AACpE,UAAI,SAAS,aAAa;AAI1B,UAAI,mBAAmB,sBAAsB;AAC5C,aAAK,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC5B,uBAAa,aAAa,CAAC;AAI3B,cAAI,mBAAmB,GAAC,8CAAY,cAAZ,mBAAuB,SAAS,YAAW;AAClE,aAAC,WAAW,cAAX,WAAW,YAAc,CAAC,IAAG,KAAK,QAAQ;AAAA,UAC5C;AAAA,QACD;AAEA,YAAI,iBAAiB;AACpB,mBAAS,KAAK;AAAA,QACf;AAAA,MACD;AAEA,WAAK,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC5B,qBAAa,aAAa,CAAC;AAE3B,YAAI;AAAA;AAAA,UAAwC;AAAA,QAAW,GAAG;AACzD;AAAA;AAAA,YAAuC;AAAA,UAAW;AAAA,QACnD;AAEA,YAAI,WAAW,KAAK,SAAS,IAAI;AAChC,iBAAO;AAAA,QACR;AAAA,MACD;AAAA,IACD;AAIA,QAAI,CAAC,cAAe,kBAAkB,QAAQ,CAAC,eAAgB;AAC9D,wBAAkB,UAAU,KAAK;AAAA,IAClC;AAAA,EACD;AAEA,SAAO;AACR;AAMA,SAAS,gBAAgB,OAAOA,SAAQ;AAEvC,MAAI,UAAUA;AAEd,SAAO,YAAY,MAAM;AACxB,SAAK,QAAQ,IAAI,qBAAqB,GAAG;AACxC,UAAI;AAEH,gBAAQ,GAAG,KAAK;AAChB;AAAA,MACD,QAAQ;AAEP,gBAAQ,KAAK;AAAA,MACd;AAAA,IACD;AAEA,cAAU,QAAQ;AAAA,EACnB;AAEA,sBAAoB;AACpB,QAAM;AACP;AAKA,SAAS,qBAAqBA,SAAQ;AACrC,UACEA,QAAO,IAAI,eAAe,MAC1BA,QAAO,WAAW,SAASA,QAAO,OAAO,IAAI,qBAAqB;AAErE;AAEO,SAAS,0BAA0B;AACzC,sBAAoB;AACrB;AAQO,SAAS,aAAa,OAAOA,SAAQ,iBAAiBC,oBAAmB;AAzShF;AA0SC,MAAI,mBAAmB;AACtB,QAAI,oBAAoB,MAAM;AAC7B,0BAAoB;AAAA,IACrB;AAEA,QAAI,qBAAqBD,OAAM,GAAG;AACjC,YAAM;AAAA,IACP;AAEA;AAAA,EACD;AAEA,MAAI,oBAAoB,MAAM;AAC7B,wBAAoB;AAAA,EACrB;AAEA,MACC,CAAC,gBACDC,uBAAsB,QACtB,EAAE,iBAAiB,UACnB,eAAe,IAAI,KAAK,GACvB;AACD,oBAAgB,OAAOD,OAAM;AAC7B;AAAA,EACD;AAEA,iBAAe,IAAI,KAAK;AAExB,QAAM,kBAAkB,CAAC;AAEzB,QAAM,eAAc,KAAAA,QAAO,OAAP,mBAAW;AAE/B,MAAI,aAAa;AAChB,oBAAgB,KAAK,WAAW;AAAA,EACjC;AAGA,MAAI,kBAAkBC;AAEtB,SAAO,oBAAoB,MAAM;AAChC,QAAI,cAAK;AAER,UAAI,YAAW,qBAAgB,aAAhB,mBAA2B;AAE1C,UAAI,UAAU;AACb,cAAM,OAAO,SAAS,MAAM,GAAG,EAAE,IAAI;AACrC,wBAAgB,KAAK,IAAI;AAAA,MAC1B;AAAA,IACD;AAEA,sBAAkB,gBAAgB;AAAA,EACnC;AAEA,QAAM,SAAS,UAAU,KAAK,UAAU,SAAS,IAAI,OAAO;AAC5D,kBAAgB,OAAO,WAAW;AAAA,IACjC,OAAO,MAAM,UAAU;AAAA,EAAK,gBAAgB,IAAI,CAAC,SAAS;AAAA,EAAK,MAAM,MAAM,IAAI,EAAE,EAAE,KAAK,EAAE,CAAC;AAAA;AAAA,EAC5F,CAAC;AACD,kBAAgB,OAAO,mBAAmB;AAAA,IACzC,OAAO;AAAA,EACR,CAAC;AAED,QAAMC,SAAQ,MAAM;AAGpB,MAAIA,QAAO;AACV,UAAM,QAAQA,OAAM,MAAM,IAAI;AAC9B,UAAM,YAAY,CAAC;AACnB,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACtC,YAAM,OAAO,MAAM,CAAC;AACpB,UAAI,KAAK,SAAS,qBAAqB,GAAG;AACzC;AAAA,MACD;AACA,gBAAU,KAAK,IAAI;AAAA,IACpB;AACA,oBAAgB,OAAO,SAAS;AAAA,MAC/B,OAAO,UAAU,KAAK,IAAI;AAAA,IAC3B,CAAC;AAAA,EACF;AAEA,kBAAgB,OAAOF,OAAM;AAE7B,MAAI,qBAAqBA,OAAM,GAAG;AACjC,UAAM;AAAA,EACP;AACD;AAOA,SAAS,2CAA2C,QAAQA,SAAQ,QAAQ,GAAG;AAC9E,MAAI,YAAY,OAAO;AACvB,MAAI,cAAc,KAAM;AAExB,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAC1C,QAAI,WAAW,UAAU,CAAC;AAC1B,SAAK,SAAS,IAAI,aAAa,GAAG;AACjC;AAAA;AAAA,QACyB;AAAA,QACxBA;AAAA,QACA,QAAQ;AAAA,MACT;AAAA,IACD,WAAWA,YAAW,UAAU;AAC/B,UAAI,UAAU,GAAG;AAChB,0BAAkB,UAAU,KAAK;AAAA,MAClC,YAAY,SAAS,IAAI,WAAW,GAAG;AACtC,0BAAkB,UAAU,WAAW;AAAA,MACxC;AACA;AAAA;AAAA,QAAuC;AAAA,MAAS;AAAA,IACjD;AAAA,EACD;AACD;AAOO,SAAS,gBAAgB,UAAU;AAja1C;AAkaC,MAAI,gBAAgB;AACpB,MAAI,wBAAwB;AAC5B,MAAI,4BAA4B;AAChC,MAAI,oBAAoB;AACxB,MAAI,yBAAyB;AAC7B,MAAI,uBAAuB;AAC3B,MAAI,6BAA6B;AACjC,MAAI,QAAQ,SAAS;AAErB;AAAA,EAA0C;AAC1C,iBAAe;AACf,qBAAmB;AACnB,qBAAmB,SAAS,gBAAgB,kBAAkB,IAAI,WAAW;AAC7E,kBAAgB,CAAC,uBAAuB,QAAQ,aAAa;AAC7D,oBAAkB;AAClB,sBAAoB,SAAS;AAC7B;AAEA,MAAI;AACH,QAAI;AAAA;AAAA,OAAkC,GAAG,SAAS,IAAI;AAAA;AACtD,QAAI,OAAO,SAAS;AAEpB,QAAI,aAAa,MAAM;AACtB,UAAI;AAEJ,uBAAiB,UAAU,YAAY;AAEvC,UAAI,SAAS,QAAQ,eAAe,GAAG;AACtC,aAAK,SAAS,eAAe,SAAS;AACtC,aAAK,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACrC,eAAK,eAAe,CAAC,IAAI,SAAS,CAAC;AAAA,QACpC;AAAA,MACD,OAAO;AACN,iBAAS,OAAO,OAAO;AAAA,MACxB;AAEA,UAAI,CAAC,eAAe;AACnB,aAAK,IAAI,cAAc,IAAI,KAAK,QAAQ,KAAK;AAC5C,YAAC,UAAK,CAAC,GAAE,cAAR,GAAQ,YAAc,CAAC,IAAG,KAAK,QAAQ;AAAA,QACzC;AAAA,MACD;AAAA,IACD,WAAW,SAAS,QAAQ,eAAe,KAAK,QAAQ;AACvD,uBAAiB,UAAU,YAAY;AACvC,WAAK,SAAS;AAAA,IACf;AAKA,QACC,SAAS,KACT,qBAAqB,SACpB,SAAS,KAAK,UAAU,cAAc,YAAY,GAClD;AACD,WAAK,IAAI,GAAG;AAAA,MAA6B,iBAAkB,QAAQ,KAAK;AACvE;AAAA,UACC,iBAAiB,CAAC;AAAA;AAAA,UACK;AAAA,QACxB;AAAA,MACD;AAAA,IACD;AAMA,QAAI,sBAAsB,MAAM;AAC/B;AAAA,IACD;AAEA,WAAO;AAAA,EACR,UAAE;AACD,eAAW;AACX,mBAAe;AACf,uBAAmB;AACnB,sBAAkB;AAClB,oBAAgB;AAChB,sBAAkB;AAClB,wBAAoB;AAAA,EACrB;AACD;AAQA,SAAS,gBAAgB,QAAQ,YAAY;AAC5C,MAAI,YAAY,WAAW;AAC3B,MAAI,cAAc,MAAM;AACvB,QAAI,QAAQ,SAAS,KAAK,WAAW,MAAM;AAC3C,QAAI,UAAU,IAAI;AACjB,UAAI,aAAa,UAAU,SAAS;AACpC,UAAI,eAAe,GAAG;AACrB,oBAAY,WAAW,YAAY;AAAA,MACpC,OAAO;AAEN,kBAAU,KAAK,IAAI,UAAU,UAAU;AACvC,kBAAU,IAAI;AAAA,MACf;AAAA,IACD;AAAA,EACD;AAGA,MACC,cAAc,SACb,WAAW,IAAI,aAAa;AAAA;AAAA;AAAA,GAI5B,aAAa,QAAQ,CAAC,SAAS,SAAS,UAAU,IAClD;AACD,sBAAkB,YAAY,WAAW;AAGzC,SAAK,WAAW,KAAK,UAAU,mBAAmB,GAAG;AACpD,iBAAW,KAAK;AAAA,IACjB;AACA;AAAA;AAAA,MAA0C;AAAA,MAAa;AAAA,IAAC;AAAA,EACzD;AACD;AAOO,SAAS,iBAAiB,QAAQ,aAAa;AACrD,MAAI,eAAe,OAAO;AAC1B,MAAI,iBAAiB,KAAM;AAE3B,WAAS,IAAI,aAAa,IAAI,aAAa,QAAQ,KAAK;AACvD,oBAAgB,QAAQ,aAAa,CAAC,CAAC;AAAA,EACxC;AACD;AAMO,SAAS,cAAcA,SAAQ;AACrC,MAAI,QAAQA,QAAO;AAEnB,OAAK,QAAQ,eAAe,GAAG;AAC9B;AAAA,EACD;AAEA,oBAAkBA,SAAQ,KAAK;AAE/B,MAAI,kBAAkB;AACtB,MAAI,6BAA6B;AAEjC,kBAAgBA;AAEhB,MAAI,cAAK;AACR,QAAI,wBAAwB;AAC5B,qCAAiCA,QAAO;AAAA,EACzC;AAEA,MAAI;AACH,SAAK,QAAQ,kBAAkB,GAAG;AACjC,oCAA8BA,OAAM;AAAA,IACrC,OAAO;AACN,8BAAwBA,OAAM;AAAA,IAC/B;AACA,4BAAwBA,OAAM;AAE9B,4BAAwBA,OAAM;AAC9B,QAAIG,YAAW,gBAAgBH,OAAM;AACrC,IAAAA,QAAO,WAAW,OAAOG,cAAa,aAAaA,YAAW;AAC9D,IAAAH,QAAO,KAAK;AAEZ,QAAI,OAAOA,QAAO;AAMlB,QAAI,gBAAO,sBAAsBA,QAAO,IAAI,WAAW,KAAK,SAAS,MAAM;AAC1E,eAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACrC,YAAI,MAAM,KAAK,CAAC;AAChB,YAAI,IAAI,qBAAqB;AAC5B,cAAI,KAAK,wBAAwB;AACjC,cAAI,sBAAsB;AAC1B,cAAI,UAAU;AAAA,QACf;AAAA,MACD;AAAA,IACD;AAEA,QAAI,cAAK;AACR,uBAAiB,KAAKA,OAAM;AAAA,IAC7B;AAAA,EACD,SAAS,OAAO;AACf,iBAAa,OAAOA,SAAQ,iBAAiB,8BAA8BA,QAAO,GAAG;AAAA,EACtF,UAAE;AACD,oBAAgB;AAEhB,QAAI,cAAK;AACR,uCAAiC;AAAA,IAClC;AAAA,EACD;AACD;AAEA,SAAS,mBAAmB;AAE3B,UAAQ;AAAA,IACP;AAAA,IACA,iBAAiB,MAAM,GAAG,EAAE,IAAI,CAAC,MAAM,EAAE,EAAE;AAAA,EAC5C;AACA,qBAAmB,CAAC;AACrB;AAEA,SAAS,sBAAsB;AAC9B,MAAI,cAAc,KAAM;AACvB,kBAAc;AACd,QAAI;AACH,MAAE,6BAA6B;AAAA,IAChC,SAAS,OAAO;AACf,UAAI,cAAK;AAER,wBAAgB,OAAO,SAAS;AAAA,UAC/B,OAAO;AAAA,QACR,CAAC;AAAA,MACF;AAGA,UAAI,0BAA0B,MAAM;AACnC,YAAI,cAAK;AACR,cAAI;AACH,yBAAa,OAAO,uBAAuB,MAAM,IAAI;AAAA,UACtD,SAAS,GAAG;AAEX,6BAAiB;AACjB,kBAAM;AAAA,UACP;AAAA,QACD,OAAO;AACN,uBAAa,OAAO,uBAAuB,MAAM,IAAI;AAAA,QACtD;AAAA,MACD,OAAO;AACN,YAAI,cAAK;AACR,2BAAiB;AAAA,QAClB;AACA,cAAM;AAAA,MACP;AAAA,IACD;AAAA,EACD;AACA;AACD;AAMA,SAAS,0BAA0B,cAAc;AAChD,MAAI,SAAS,aAAa;AAC1B,MAAI,WAAW,GAAG;AACjB;AAAA,EACD;AACA,sBAAoB;AAEpB,MAAI,6BAA6B;AACjC,uBAAqB;AAErB,MAAI;AACH,aAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAChC,UAAIA,UAAS,aAAa,CAAC;AAE3B,WAAKA,QAAO,IAAI,WAAW,GAAG;AAC7B,QAAAA,QAAO,KAAK;AAAA,MACb;AAGA,UAAI,oBAAoB,CAAC;AAEzB,sBAAgBA,SAAQ,iBAAiB;AACzC,2BAAqB,iBAAiB;AAAA,IACvC;AAAA,EACD,UAAE;AACD,yBAAqB;AAAA,EACtB;AACD;AAMA,SAAS,qBAAqB,SAAS;AACtC,MAAI,SAAS,QAAQ;AACrB,MAAI,WAAW,EAAG;AAElB,WAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAChC,QAAIA,UAAS,QAAQ,CAAC;AAEtB,SAAKA,QAAO,KAAK,YAAY,YAAY,GAAG;AAC3C,UAAI;AACH,YAAI,gBAAgBA,OAAM,GAAG;AAC5B,wBAAcA,OAAM;AAOpB,cAAIA,QAAO,SAAS,QAAQA,QAAO,UAAU,QAAQA,QAAO,gBAAgB,MAAM;AACjF,gBAAIA,QAAO,aAAa,MAAM;AAE7B,4BAAcA,OAAM;AAAA,YACrB,OAAO;AAEN,cAAAA,QAAO,KAAK;AAAA,YACb;AAAA,UACD;AAAA,QACD;AAAA,MACD,SAAS,OAAO;AACf,qBAAa,OAAOA,SAAQ,MAAMA,QAAO,GAAG;AAAA,MAC7C;AAAA,IACD;AAAA,EACD;AACD;AAEA,SAAS,mBAAmB;AAC3B,EAAAD,wBAAuB;AACvB,MAAI,cAAc,MAAM;AACvB;AAAA,EACD;AACA,QAAM,+BAA+B;AACrC,wBAAsB,CAAC;AACvB,4BAA0B,4BAA4B;AAEtD,MAAI,CAACA,uBAAsB;AAC1B,kBAAc;AACd,4BAAwB;AACxB,QAAI,cAAK;AACR,yBAAmB,CAAC;AAAA,IACrB;AAAA,EACD;AACD;AAMO,SAAS,gBAAgB,QAAQ;AACvC,MAAI,mBAAmB,iBAAiB;AACvC,QAAI,CAACA,uBAAsB;AAC1B,MAAAA,wBAAuB;AACvB,qBAAe,gBAAgB;AAAA,IAChC;AAAA,EACD;AAEA,0BAAwB;AAExB,MAAIC,UAAS;AAEb,SAAOA,QAAO,WAAW,MAAM;AAC9B,IAAAA,UAASA,QAAO;AAChB,QAAI,QAAQA,QAAO;AAEnB,SAAK,SAAS,cAAc,oBAAoB,GAAG;AAClD,WAAK,QAAQ,WAAW,EAAG;AAC3B,MAAAA,QAAO,KAAK;AAAA,IACb;AAAA,EACD;AAEA,sBAAoB,KAAKA,OAAM;AAChC;AAaA,SAAS,gBAAgBA,SAAQ,mBAAmB;AACnD,MAAI,iBAAiBA,QAAO;AAC5B,MAAI,UAAU,CAAC;AAEf,YAAW,QAAO,mBAAmB,MAAM;AAC1C,QAAI,QAAQ,eAAe;AAC3B,QAAI,aAAa,QAAQ,mBAAmB;AAC5C,QAAI,sBAAsB,cAAc,QAAQ,WAAW;AAC3D,QAAII,WAAU,eAAe;AAE7B,QAAI,CAAC,wBAAwB,QAAQ,WAAW,GAAG;AAClD,WAAK,QAAQ,mBAAmB,GAAG;AAClC,YAAI,WAAW;AACd,yBAAe,KAAK;AAAA,QACrB,OAAO;AACN,cAAI;AACH,gBAAI,gBAAgB,cAAc,GAAG;AACpC,4BAAc,cAAc;AAAA,YAC7B;AAAA,UACD,SAAS,OAAO;AACf,yBAAa,OAAO,gBAAgB,MAAM,eAAe,GAAG;AAAA,UAC7D;AAAA,QACD;AAEA,YAAIC,SAAQ,eAAe;AAE3B,YAAIA,WAAU,MAAM;AACnB,2BAAiBA;AACjB;AAAA,QACD;AAAA,MACD,YAAY,QAAQ,YAAY,GAAG;AAClC,gBAAQ,KAAK,cAAc;AAAA,MAC5B;AAAA,IACD;AAEA,QAAID,aAAY,MAAM;AACrB,UAAI,SAAS,eAAe;AAE5B,aAAO,WAAW,MAAM;AACvB,YAAIJ,YAAW,QAAQ;AACtB,gBAAM;AAAA,QACP;AACA,YAAI,iBAAiB,OAAO;AAC5B,YAAI,mBAAmB,MAAM;AAC5B,2BAAiB;AACjB,mBAAS;AAAA,QACV;AACA,iBAAS,OAAO;AAAA,MACjB;AAAA,IACD;AAEA,qBAAiBI;AAAA,EAClB;AAIA,WAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACxC,IAAAC,SAAQ,QAAQ,CAAC;AACjB,sBAAkB,KAAKA,MAAK;AAC5B,oBAAgBA,QAAO,iBAAiB;AAAA,EACzC;AACD;AAQO,SAAS,WAAW,IAAI;AAC9B,MAAI,0BAA0B;AAC9B,MAAI,+BAA+B;AAEnC,MAAI;AACH,wBAAoB;AAGpB,UAAM,eAAe,CAAC;AAEtB,qBAAiB;AACjB,0BAAsB;AACtB,IAAAN,wBAAuB;AAEvB,8BAA0B,4BAA4B;AAEtD,QAAI,SAAS;AAEb,gBAAY;AACZ,QAAI,oBAAoB,SAAS,KAAK,aAAa,SAAS,GAAG;AAC9D,iBAAW;AAAA,IACZ;AAEA,kBAAc;AACd,4BAAwB;AACxB,QAAI,cAAK;AACR,yBAAmB,CAAC;AAAA,IACrB;AAEA,WAAO;AAAA,EACR,UAAE;AACD,qBAAiB;AACjB,0BAAsB;AAAA,EACvB;AACD;AAMA,eAAsB,OAAO;AAC5B,QAAM,QAAQ,QAAQ;AAGtB,aAAW;AACZ;AAOO,SAAS,IAAI,QAAQ;AAt5B5B;AAu5BC,MAAI,QAAQ,OAAO;AACnB,MAAI,cAAc,QAAQ,aAAa;AAIvC,MAAI,eAAe,QAAQ,eAAe,GAAG;AAC5C,QAAI,QAAQ;AAAA;AAAA,MAAwC;AAAA,IAAO;AAE3D;AAAA;AAAA,MAAwC;AAAA,IAAO;AAC/C,WAAO;AAAA,EACR;AAEA,MAAI,qBAAqB,MAAM;AAC9B,qBAAiB,IAAI,MAAM;AAAA,EAC5B;AAGA,MAAI,oBAAoB,MAAM;AAC7B,QAAI,oBAAoB,QAAQ,gBAAgB,SAAS,MAAM,GAAG;AACjE,MAAE,wBAAwB;AAAA,IAC3B;AACA,QAAI,OAAO,gBAAgB;AAC3B,QAAI,OAAO,KAAK,cAAc;AAC7B,aAAO,KAAK;AAIZ,UAAI,aAAa,QAAQ,SAAS,QAAQ,KAAK,YAAY,MAAM,QAAQ;AACxE;AAAA,MACD,WAAW,aAAa,MAAM;AAC7B,mBAAW,CAAC,MAAM;AAAA,MACnB,OAAO;AACN,iBAAS,KAAK,MAAM;AAAA,MACrB;AAAA,IACD;AAAA,EACD,WAAW;AAAA,EAAsC,OAAQ,SAAS,MAAM;AACvE,QAAIO;AAAA;AAAA,MAAkC;AAAA;AACtC,QAAI,SAASA,SAAQ;AACrB,QAAI,SAASA;AAEb,WAAO,WAAW,MAAM;AAGvB,WAAK,OAAO,IAAI,aAAa,GAAG;AAC/B,YAAI;AAAA;AAAA,UAAyC;AAAA;AAE7C,iBAAS;AACT,iBAAS,eAAe;AAAA,MACzB,OAAO;AACN,YAAI;AAAA;AAAA,UAAuC;AAAA;AAE3C,YAAI,GAAC,mBAAc,aAAd,mBAAwB,SAAS,UAAS;AAC9C,WAAC,cAAc,aAAd,cAAc,WAAa,CAAC,IAAG,KAAK,MAAM;AAAA,QAC5C;AACA;AAAA,MACD;AAAA,IACD;AAAA,EACD;AAEA,MAAI,YAAY;AACf,IAAAA;AAAA,IAAkC;AAElC,QAAI,gBAAgBA,QAAO,GAAG;AAC7B,qBAAeA,QAAO;AAAA,IACvB;AAAA,EACD;AAEA,MACC,gBACA,qBACA,wBAAwB,QACxB,oBAAoB,QACpB,oBAAoB,aAAa,iBAChC;AAED,QAAI,OAAO,OAAO;AACjB,aAAO,MAAM;AAAA,IACd,WAAW,OAAO,SAAS;AAC1B,UAAI,QAAQ,oBAAoB,QAAQ,IAAI,MAAM;AAElD,UAAI,UAAU,QAAW;AACxB,gBAAQ,EAAE,MAAM,CAAC,EAAE;AACnB,4BAAoB,QAAQ,IAAI,QAAQ,KAAK;AAAA,MAC9C;AAEA,YAAM,KAAK,KAAKC,WAAU,UAAU,CAAC;AAAA,IACtC;AAAA,EACD;AAEA,SAAO,OAAO;AACf;AAQO,SAAS,SAAS,QAAQ;AAChC,SAAO,UAAU,IAAI,MAAM;AAC5B;AAOO,SAAS,gBAAgB,IAAI;AACnC,MAAI,4BAA4B;AAChC,qBAAmB,oBAAI,IAAI;AAE3B,MAAI,WAAW;AACf,MAAI;AAEJ,MAAI;AACH,YAAQ,EAAE;AACV,QAAI,8BAA8B,MAAM;AACvC,WAAK,UAAU,kBAAkB;AAChC,kCAA0B,IAAI,MAAM;AAAA,MACrC;AAAA,IACD;AAAA,EACD,UAAE;AACD,uBAAmB;AAAA,EACpB;AAEA,SAAO;AACR;AAOO,SAAS,yBAAyB,IAAI;AAC5C,MAAI,WAAW,gBAAgB,MAAM,QAAQ,EAAE,CAAC;AAEhD,WAAS,UAAU,UAAU;AAE5B,SAAK,OAAO,IAAI,yBAAyB,GAAG;AAC3C;AAAA,cAAW;AAAA;AAAA,QAA+B,OAAQ,QAAQ,CAAC;AAAA,QAAG;AAC7D,aAAK,IAAI,IAAI,aAAa,GAAG;AAE5B,uBAAa,KAAK,IAAI,CAAC;AAAA,QACxB;AAAA,MACD;AAAA,IACD,OAAO;AACN,mBAAa,QAAQ,OAAO,CAAC;AAAA,IAC9B;AAAA,EACD;AACD;AAkBO,SAAS,QAAQ,IAAI;AAC3B,QAAM,oBAAoB;AAC1B,MAAI;AACH,sBAAkB;AAClB,WAAO,GAAG;AAAA,EACX,UAAE;AACD,sBAAkB;AAAA,EACnB;AACD;AAEA,IAAM,cAAc,EAAE,QAAQ,cAAc;AAOrC,SAAS,kBAAkB,QAAQ,QAAQ;AACjD,SAAO,IAAK,OAAO,IAAI,cAAe;AACvC;AAUO,SAAS,WAAW,KAAK;AAC/B,QAAM,cAAc,wBAAwB,YAAY;AACxD,QAAM;AAAA;AAAA,IAA2B,YAAY,IAAI,GAAG;AAAA;AAEpD,MAAI,cAAK;AACR,UAAM;AAAA;AAAA,MAAsC,kBAAmB;AAAA;AAC/D,QAAI,IAAI;AACP,gBAAU,QAAQ,IAAI,IAAI;AAAA,IAC3B;AAAA,EACD;AAEA,SAAO;AACR;AAcO,SAAS,WAAW,KAAK,SAAS;AACxC,QAAM,cAAc,wBAAwB,YAAY;AACxD,cAAY,IAAI,KAAK,OAAO;AAC5B,SAAO;AACR;AASO,SAAS,WAAW,KAAK;AAC/B,QAAM,cAAc,wBAAwB,YAAY;AACxD,SAAO,YAAY,IAAI,GAAG;AAC3B;AAUO,SAAS,iBAAiB;AAChC,QAAM,cAAc,wBAAwB,gBAAgB;AAE5D,MAAI,cAAK;AACR,UAAM,KAAK,uDAAmB;AAC9B,QAAI,IAAI;AACP,iBAAW,SAAS,YAAY,OAAO,GAAG;AACzC,kBAAU,OAAO,IAAI,IAAI;AAAA,MAC1B;AAAA,IACD;AAAA,EACD;AAEA;AAAA;AAAA,IAAyB;AAAA;AAC1B;AAMA,SAAS,wBAAwB,MAAM;AACtC,MAAI,sBAAsB,MAAM;AAC/B,gCAA4B,IAAI;AAAA,EACjC;AAEA,SAAQ,kBAAkB,MAAlB,kBAAkB,IAAM,IAAI,IAAI,mBAAmB,iBAAiB,KAAK,MAAS;AAC3F;AAMA,SAAS,mBAAmBN,oBAAmB;AAC9C,MAAI,SAASA,mBAAkB;AAC/B,SAAO,WAAW,MAAM;AACvB,UAAM,cAAc,OAAO;AAC3B,QAAI,gBAAgB,MAAM;AACzB,aAAO;AAAA,IACR;AACA,aAAS,OAAO;AAAA,EACjB;AACA,SAAO;AACR;AAQO,SAAS,OAAO,QAAQ,IAAI,GAAG;AACrC,MAAI,QAAQ,IAAI,MAAM;AACtB,MAAI,SAAS,MAAM,IAAI,UAAU;AAEjC,MAAI,QAAQ,KAAK;AAGjB,SAAO;AACR;AAQO,SAAS,WAAW,QAAQ,IAAI,GAAG;AACzC,MAAI,QAAQ,IAAI,MAAM;AAGtB,SAAO,IAAI,QAAQ,MAAM,IAAI,EAAE,QAAQ,EAAE,KAAK;AAC/C;AAOO,SAAS,oBAAoB,KAAK,MAAM;AAE9C,MAAI,SAAS,CAAC;AAEd,WAAS,OAAO,KAAK;AACpB,QAAI,CAAC,KAAK,SAAS,GAAG,GAAG;AACxB,aAAO,GAAG,IAAI,IAAI,GAAG;AAAA,IACtB;AAAA,EACD;AAEA,SAAO;AACR;AAQO,SAAS,KAAK,OAAO,QAAQ,OAAO,IAAI;AAC9C,sBAAoB;AAAA,IACnB,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,EACJ;AAEA,MAAI,oBAAoB,CAAC,OAAO;AAC/B,sBAAkB,IAAI;AAAA,MACrB,GAAG;AAAA,MACH,GAAG;AAAA,MACH,IAAI,CAAC;AAAA,MACL,IAAI,OAAO,KAAK;AAAA,IACjB;AAAA,EACD;AAEA,MAAI,cAAK;AAER,sBAAkB,WAAW;AAC7B,qCAAiC;AAAA,EAClC;AACD;AAOO,SAAS,IAAI,WAAW;AA/wC/B;AAgxCC,QAAM,qBAAqB;AAC3B,MAAI,uBAAuB,MAAM;AAChC,QAAI,cAAc,QAAW;AAC5B,yBAAmB,IAAI;AAAA,IACxB;AACA,UAAM,oBAAoB,mBAAmB;AAC7C,QAAI,sBAAsB,MAAM;AAC/B,UAAI,kBAAkB;AACtB,UAAI,oBAAoB;AACxB,yBAAmB,IAAI;AACvB,UAAI;AACH,iBAAS,IAAI,GAAG,IAAI,kBAAkB,QAAQ,KAAK;AAClD,cAAI,mBAAmB,kBAAkB,CAAC;AAC1C,4BAAkB,iBAAiB,MAAM;AACzC,8BAAoB,iBAAiB,QAAQ;AAC7C,iBAAO,iBAAiB,EAAE;AAAA,QAC3B;AAAA,MACD,UAAE;AACD,0BAAkB,eAAe;AACjC,4BAAoB,iBAAiB;AAAA,MACtC;AAAA,IACD;AACA,wBAAoB,mBAAmB;AACvC,QAAI,cAAK;AACR,yCAAiC,wBAAmB,MAAnB,mBAAsB,aAAY;AAAA,IACpE;AACA,uBAAmB,IAAI;AAAA,EACxB;AAGA,SAAO;AAAA,EAA+B,CAAC;AACxC;AAQO,SAAS,gBAAgB,OAAO;AACtC,MAAI,OAAO,UAAU,YAAY,CAAC,SAAS,iBAAiB,aAAa;AACxE;AAAA,EACD;AAEA,MAAI,gBAAgB,OAAO;AAC1B,cAAU,KAAK;AAAA,EAChB,WAAW,CAAC,MAAM,QAAQ,KAAK,GAAG;AACjC,aAAS,OAAO,OAAO;AACtB,YAAM,OAAO,MAAM,GAAG;AACtB,UAAI,OAAO,SAAS,YAAY,QAAQ,gBAAgB,MAAM;AAC7D,kBAAU,IAAI;AAAA,MACf;AAAA,IACD;AAAA,EACD;AACD;AASO,SAAS,UAAU,OAAO,UAAU,oBAAI,IAAI,GAAG;AACrD,MACC,OAAO,UAAU,YACjB,UAAU;AAAA,EAEV,EAAE,iBAAiB,gBACnB,CAAC,QAAQ,IAAI,KAAK,GACjB;AACD,YAAQ,IAAI,KAAK;AAGjB,QAAI,iBAAiB,MAAM;AAC1B,YAAM,QAAQ;AAAA,IACf;AACA,aAAS,OAAO,OAAO;AACtB,UAAI;AACH,kBAAU,MAAM,GAAG,GAAG,OAAO;AAAA,MAC9B,SAAS,GAAG;AAAA,MAEZ;AAAA,IACD;AACA,UAAM,QAAQ,iBAAiB,KAAK;AACpC,QACC,UAAU,OAAO,aACjB,UAAU,MAAM,aAChB,UAAU,IAAI,aACd,UAAU,IAAI,aACd,UAAU,KAAK,WACd;AACD,YAAM,cAAc,gBAAgB,KAAK;AACzC,eAAS,OAAO,aAAa;AAC5B,cAAMO,OAAM,YAAY,GAAG,EAAE;AAC7B,YAAIA,MAAK;AACR,cAAI;AACH,YAAAA,KAAI,KAAK,KAAK;AAAA,UACf,SAAS,GAAG;AAAA,UAEZ;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAAA,EACD;AACD;AAEA,IAAI,cAAK;AAIR,MAAS,mBAAT,SAA0B,MAAM;AAC/B,QAAI,EAAE,QAAQ,aAAa;AAG1B,UAAI;AACJ,aAAO,eAAe,YAAY,MAAM;AAAA,QACvC,cAAc;AAAA;AAAA,QAEd,KAAK,MAAM;AACV,cAAI,UAAU,QAAW;AACxB,mBAAO;AAAA,UACR;AAEA,UAAE,oBAAoB,IAAI;AAAA,QAC3B;AAAA,QACA,KAAK,CAAC,MAAM;AACX,kBAAQ;AAAA,QACT;AAAA,MACD,CAAC;AAAA,IACF;AAAA,EACD;AAEA,mBAAiB,QAAQ;AACzB,mBAAiB,SAAS;AAC1B,mBAAiB,UAAU;AAC3B,mBAAiB,UAAU;AAC3B,mBAAiB,QAAQ;AACzB,mBAAiB,WAAW;AAC7B;;;AC54CO,IAAI,YAAY;AAGhB,SAAS,cAAc,OAAO;AACpC,cAAY;AACb;AASO,IAAI;AAGJ,SAAS,iBAAiB,MAAM;AACtC,MAAI,SAAS,MAAM;AAClB,IAAE,mBAAmB;AACrB,UAAM;AAAA,EACP;AAEA,SAAQ,eAAe;AACxB;AAEO,SAAS,eAAe;AAC9B,SAAO;AAAA;AAAA,IAA8C,iBAAiB,YAAY;AAAA,EAAE;AACrF;AAGO,SAAS,MAAM,MAAM;AAC3B,MAAI,CAAC,UAAW;AAGhB,MAAI,iBAAiB,YAAY,MAAM,MAAM;AAC5C,IAAE,mBAAmB;AACrB,UAAM;AAAA,EACP;AAEA,iBAAe;AAChB;AAKO,SAAS,iBAAiB,UAAU;AAC1C,MAAI,WAAW;AAEd,mBAAe,SAAS;AAAA,EACzB;AACD;AAEO,SAAS,KAAK,QAAQ,GAAG;AAC/B,MAAI,WAAW;AACd,QAAI,IAAI;AACR,QAAI,OAAO;AAEX,WAAO,KAAK;AACX;AAAA,MAAoC,iBAAiB,IAAI;AAAA,IAC1D;AAEA,mBAAe;AAAA,EAChB;AACD;AAKO,SAAS,eAAe;AAC9B,MAAI,QAAQ;AACZ,MAAI,OAAO;AAEX,SAAO,MAAM;AACZ,QAAI,KAAK,aAAa,GAAG;AACxB,UAAI;AAAA;AAAA,QAA+B,KAAM;AAAA;AAEzC,UAAI,SAAS,eAAe;AAC3B,YAAI,UAAU,EAAG,QAAO;AACxB,iBAAS;AAAA,MACV,WAAW,SAAS,mBAAmB,SAAS,sBAAsB;AACrE,iBAAS;AAAA,MACV;AAAA,IACD;AAEA,QAAIC;AAAA;AAAA,MAAoC,iBAAiB,IAAI;AAAA;AAC7D,SAAK,OAAO;AACZ,WAAOA;AAAA,EACR;AACD;;;AC/EO,SAAS,MAAM,OAAO,SAAS,MAAM,MAAM;AAzBlD;AA2BC,MAAIC,SAAQ;AACZ,MAAI,gBAAO,mBAAmB;AAC7B,IAAAA,SAAQC,WAAU,WAAW;AAAA,EAC9B;AAEA,MAAI,OAAO,UAAU,YAAY,UAAU,QAAQ,gBAAgB,OAAO;AACzE,WAAO;AAAA,EACR;AAEA,QAAM,YAAY,iBAAiB,KAAK;AAExC,MAAI,cAAc,oBAAoB,cAAc,iBAAiB;AACpE,WAAO;AAAA,EACR;AAGA,MAAI,UAAU,oBAAI,IAAI;AACtB,MAAI,mBAAmB,SAAS,KAAK;AACrC,MAAI,UAAU,OAAO,CAAC;AAEtB,MAAI,kBAAkB;AAGrB,YAAQ,IAAI,UAAU;AAAA;AAAA,MAA6B,MAAO;AAAA,MAAQD;AAAA,IAAK,CAAC;AAAA,EACzE;AAGA,MAAI;AAEJ,MAAI,cAAK;AACR,eAAW;AAAA,MACV;AAAA,MACA,QAAQ;AAAA,IACT;AAEA,QAAI,MAAM;AAIT,YAAM,eAAc,gBAAK,MAAL,mBAAS,2BAAT,mBAAiC;AACrD,eAAS,SAAS,cAAc,IAAI,IAAI,WAAW,IAAI;AAAA,IACxD,OAAO;AACN,eAAS,SACR,WAAW,OACR,sBAAsB,OACrB,oBAAI,IAAI,CAAC,kBAAkB,QAAQ,CAAC,IACpC,OACD,oBAAI,IAAI;AAAA,IACb;AAAA,EACD;AAEA,SAAO,IAAI;AAAA;AAAA,IAA0B;AAAA,IAAQ;AAAA,MAC5C,eAAe,GAAG,MAAM,YAAY;AACnC,YACC,EAAE,WAAW,eACb,WAAW,iBAAiB,SAC5B,WAAW,eAAe,SAC1B,WAAW,aAAa,OACvB;AAKD,UAAE,wBAAwB;AAAA,QAC3B;AAEA,YAAI,IAAI,QAAQ,IAAI,IAAI;AAExB,YAAI,MAAM,QAAW;AACpB,cAAI,OAAO,WAAW,OAAOA,MAAK;AAClC,kBAAQ,IAAI,MAAM,CAAC;AAAA,QACpB,OAAO;AACN,cAAI,GAAG,MAAM,WAAW,OAAO,QAAQ,CAAC;AAAA,QACzC;AAEA,eAAO;AAAA,MACR;AAAA,MAEA,eAAe,QAAQ,MAAM;AAC5B,YAAI,IAAI,QAAQ,IAAI,IAAI;AAExB,YAAI,MAAM,QAAW;AACpB,cAAI,QAAQ,QAAQ;AACnB,oBAAQ,IAAI,MAAM,OAAO,eAAeA,MAAK,CAAC;AAAA,UAC/C;AAAA,QACD,OAAO;AAGN,cAAI,oBAAoB,OAAO,SAAS,UAAU;AACjD,gBAAI;AAAA;AAAA,cAAoC,QAAQ,IAAI,QAAQ;AAAA;AAC5D,gBAAI,IAAI,OAAO,IAAI;AAEnB,gBAAI,OAAO,UAAU,CAAC,KAAK,IAAI,GAAG,GAAG;AACpC,kBAAI,IAAI,CAAC;AAAA,YACV;AAAA,UACD;AACA,cAAI,GAAG,aAAa;AACpB,yBAAe,OAAO;AAAA,QACvB;AAEA,eAAO;AAAA,MACR;AAAA,MAEA,IAAI,QAAQ,MAAM,UAAU;AAlI9B,YAAAE;AAmIG,YAAI,gBAAO,SAAS,uBAAuB;AAC1C,iBAAO;AAAA,QACR;AAEA,YAAI,SAAS,cAAc;AAC1B,iBAAO;AAAA,QACR;AAEA,YAAI,IAAI,QAAQ,IAAI,IAAI;AACxB,YAAI,SAAS,QAAQ;AAGrB,YAAI,MAAM,WAAc,CAAC,YAAUA,MAAA,eAAe,QAAQ,IAAI,MAA3B,gBAAAA,IAA8B,YAAW;AAC3E,cAAI,OAAO,MAAM,SAAS,OAAO,IAAI,IAAI,eAAe,QAAQ,GAAGF,MAAK;AACxE,kBAAQ,IAAI,MAAM,CAAC;AAAA,QACpB;AAEA,YAAI,MAAM,QAAW;AACpB,cAAI,IAAI,IAAI,CAAC;AASb,cAAI,cAAK;AAER,gBAAI,gBAAgB,uBAAI;AACxB,gBAAI,kBAAiB,+CAAe,YAAW,UAAU;AACxD,8BAAgB,UAAU,aAAa;AAAA,YACxC;AAAA,UACD;AAEA,iBAAO,MAAM,gBAAgB,SAAY;AAAA,QAC1C;AAEA,eAAO,QAAQ,IAAI,QAAQ,MAAM,QAAQ;AAAA,MAC1C;AAAA,MAEA,yBAAyB,QAAQ,MAAM;AACtC,YAAI,aAAa,QAAQ,yBAAyB,QAAQ,IAAI;AAE9D,YAAI,cAAc,WAAW,YAAY;AACxC,cAAI,IAAI,QAAQ,IAAI,IAAI;AACxB,cAAI,EAAG,YAAW,QAAQ,IAAI,CAAC;AAAA,QAChC,WAAW,eAAe,QAAW;AACpC,cAAIG,UAAS,QAAQ,IAAI,IAAI;AAC7B,cAAIC,SAAQD,WAAA,gBAAAA,QAAQ;AAEpB,cAAIA,YAAW,UAAaC,WAAU,eAAe;AACpD,mBAAO;AAAA,cACN,YAAY;AAAA,cACZ,cAAc;AAAA,cACd,OAAAA;AAAA,cACA,UAAU;AAAA,YACX;AAAA,UACD;AAAA,QACD;AAEA,eAAO;AAAA,MACR;AAAA,MAEA,IAAI,QAAQ,MAAM;AAnMpB,YAAAF;AAoMG,YAAI,gBAAO,SAAS,uBAAuB;AAC1C,iBAAO;AAAA,QACR;AAEA,YAAI,SAAS,cAAc;AAC1B,iBAAO;AAAA,QACR;AAEA,YAAI,IAAI,QAAQ,IAAI,IAAI;AACxB,YAAI,MAAO,MAAM,UAAa,EAAE,MAAM,iBAAkB,QAAQ,IAAI,QAAQ,IAAI;AAEhF,YACC,MAAM,UACL,kBAAkB,SAAS,CAAC,SAAOA,MAAA,eAAe,QAAQ,IAAI,MAA3B,gBAAAA,IAA8B,YACjE;AACD,cAAI,MAAM,QAAW;AACpB,gBAAI,OAAO,MAAM,MAAM,OAAO,IAAI,GAAG,QAAQ,IAAI,eAAeF,MAAK;AACrE,oBAAQ,IAAI,MAAM,CAAC;AAAA,UACpB;AAEA,cAAII,SAAQ,IAAI,CAAC;AACjB,cAAIA,WAAU,eAAe;AAC5B,mBAAO;AAAA,UACR;AAAA,QACD;AAEA,eAAO;AAAA,MACR;AAAA,MAEA,IAAI,QAAQ,MAAMA,QAAO,UAAU;AAjOrC,YAAAF;AAkOG,YAAI,IAAI,QAAQ,IAAI,IAAI;AACxB,YAAI,MAAM,QAAQ;AAGlB,YAAI,oBAAoB,SAAS,UAAU;AAC1C,mBAAS,IAAIE,QAAO;AAAA,UAAmC,EAAG,GAAG,KAAK,GAAG;AACpE,gBAAI,UAAU,QAAQ,IAAI,IAAI,EAAE;AAChC,gBAAI,YAAY,QAAW;AAC1B,kBAAI,SAAS,aAAa;AAAA,YAC3B,WAAW,KAAK,QAAQ;AAIvB,wBAAU,OAAO,eAAeJ,MAAK;AACrC,sBAAQ,IAAI,IAAI,IAAI,OAAO;AAAA,YAC5B;AAAA,UACD;AAAA,QACD;AAMA,YAAI,MAAM,QAAW;AACpB,cAAI,CAAC,SAAOE,MAAA,eAAe,QAAQ,IAAI,MAA3B,gBAAAA,IAA8B,WAAU;AACnD,gBAAI,OAAO,QAAWF,MAAK;AAC3B,gBAAI,GAAG,MAAMI,QAAO,QAAQ,CAAC;AAC7B,oBAAQ,IAAI,MAAM,CAAC;AAAA,UACpB;AAAA,QACD,OAAO;AACN,gBAAM,EAAE,MAAM;AACd,cAAI,GAAG,MAAMA,QAAO,QAAQ,CAAC;AAAA,QAC9B;AAEA,YAAI,cAAK;AAER,cAAI,gBAAgBA,UAAA,gBAAAA,OAAQ;AAC5B,cAAI,kBAAiB,+CAAe,YAAW,UAAU;AACxD,4BAAgB,UAAU,aAAa;AAAA,UACxC;AACA,0BAAgB,QAAQ;AAAA,QACzB;AAEA,YAAI,aAAa,QAAQ,yBAAyB,QAAQ,IAAI;AAG9D,YAAI,yCAAY,KAAK;AACpB,qBAAW,IAAI,KAAK,UAAUA,MAAK;AAAA,QACpC;AAEA,YAAI,CAAC,KAAK;AAKT,cAAI,oBAAoB,OAAO,SAAS,UAAU;AACjD,gBAAI;AAAA;AAAA,cAAoC,QAAQ,IAAI,QAAQ;AAAA;AAC5D,gBAAI,IAAI,OAAO,IAAI;AAEnB,gBAAI,OAAO,UAAU,CAAC,KAAK,KAAK,GAAG,GAAG;AACrC,kBAAI,IAAI,IAAI,CAAC;AAAA,YACd;AAAA,UACD;AAEA,yBAAe,OAAO;AAAA,QACvB;AAEA,eAAO;AAAA,MACR;AAAA,MAEA,QAAQ,QAAQ;AACf,YAAI,OAAO;AAEX,YAAI,WAAW,QAAQ,QAAQ,MAAM,EAAE,OAAO,CAACC,SAAQ;AACtD,cAAIF,UAAS,QAAQ,IAAIE,IAAG;AAC5B,iBAAOF,YAAW,UAAaA,QAAO,MAAM;AAAA,QAC7C,CAAC;AAED,iBAAS,CAAC,KAAKA,OAAM,KAAK,SAAS;AAClC,cAAIA,QAAO,MAAM,iBAAiB,EAAE,OAAO,SAAS;AACnD,qBAAS,KAAK,GAAG;AAAA,UAClB;AAAA,QACD;AAEA,eAAO;AAAA,MACR;AAAA,MAEA,iBAAiB;AAChB,QAAE,sBAAsB;AAAA,MACzB;AAAA,IACD;AAAA,EAAC;AACF;AAMA,SAAS,eAAe,QAAQ,IAAI,GAAG;AACtC,MAAI,QAAQ,OAAO,IAAI,CAAC;AACzB;AAKO,SAAS,kBAAkB,OAAO;AACxC,MAAI,UAAU,QAAQ,OAAO,UAAU,YAAY,gBAAgB,OAAO;AACzE,WAAO,MAAM,YAAY;AAAA,EAC1B;AAEA,SAAO;AACR;AAMO,SAAS,GAAG,GAAG,GAAG;AACxB,SAAO,OAAO,GAAG,kBAAkB,CAAC,GAAG,kBAAkB,CAAC,CAAC;AAC5D;;;ACrVO,SAAS,gCAAgC;AAC/C,QAAMG,mBAAkB,MAAM;AAI9B,QAAM,UAAU,MAAM;AACtB,MAAI,SAAS;AACZ,YAAQ;AAAA,EACT;AAEA,QAAM,EAAE,SAAS,aAAa,SAAS,IAAIA;AAE3C,EAAAA,iBAAgB,UAAU,SAAU,MAAM,YAAY;AACrD,UAAM,QAAQ,QAAQ,KAAK,MAAM,MAAM,UAAU;AAEjD,QAAI,UAAU,IAAI;AACjB,eAAS,IAAI,cAAc,GAAG,IAAI,KAAK,QAAQ,KAAK,GAAG;AACtD,YAAI,kBAAkB,KAAK,CAAC,CAAC,MAAM,MAAM;AACxC,UAAE,8BAA8B,oBAAoB;AACpD;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAEA,WAAO;AAAA,EACR;AAEA,EAAAA,iBAAgB,cAAc,SAAU,MAAM,YAAY;AAGzD,UAAM,QAAQ,YAAY,KAAK,MAAM,MAAM,cAAc,KAAK,SAAS,CAAC;AAExE,QAAI,UAAU,IAAI;AACjB,eAAS,IAAI,GAAG,MAAM,cAAc,KAAK,SAAS,IAAI,KAAK,GAAG;AAC7D,YAAI,kBAAkB,KAAK,CAAC,CAAC,MAAM,MAAM;AACxC,UAAE,8BAA8B,wBAAwB;AACxD;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAEA,WAAO;AAAA,EACR;AAEA,EAAAA,iBAAgB,WAAW,SAAU,MAAM,YAAY;AACtD,UAAM,MAAM,SAAS,KAAK,MAAM,MAAM,UAAU;AAEhD,QAAI,CAAC,KAAK;AACT,eAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK,GAAG;AACxC,YAAI,kBAAkB,KAAK,CAAC,CAAC,MAAM,MAAM;AACxC,UAAE,8BAA8B,qBAAqB;AACrD;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAEA,WAAO;AAAA,EACR;AAGA,QAAM,mBAAmB,MAAM;AAC9B,IAAAA,iBAAgB,UAAU;AAC1B,IAAAA,iBAAgB,cAAc;AAC9B,IAAAA,iBAAgB,WAAW;AAAA,EAC5B;AACD;AAQO,SAAS,cAAc,GAAG,GAAG,QAAQ,MAAM;AAGjD,MAAI;AACH,QAAK,MAAM,OAAQ,kBAAkB,CAAC,MAAM,kBAAkB,CAAC,IAAI;AAClE,MAAE,8BAA8B,QAAQ,QAAQ,KAAK;AAAA,IACtD;AAAA,EACD,QAAQ;AAAA,EAAC;AAET,SAAQ,MAAM,MAAO;AACtB;AAQO,SAASC,QAAO,GAAG,GAAG,QAAQ,MAAM;AAC1C,MAAK,KAAK,OAAQ,kBAAkB,CAAC,KAAK,kBAAkB,CAAC,IAAI;AAChE,IAAE,8BAA8B,QAAQ,OAAO,IAAI;AAAA,EACpD;AAEA,SAAQ,KAAK,MAAO;AACrB;;;AC5FO,IAAI;AAGJ,IAAI;AAGX,IAAI;AAEJ,IAAI;AAMG,SAAS,kBAAkB;AACjC,MAAI,YAAY,QAAW;AAC1B;AAAA,EACD;AAEA,YAAU;AACV,cAAY;AAEZ,MAAI,oBAAoB,QAAQ;AAChC,MAAI,iBAAiB,KAAK;AAG1B,uBAAqB,eAAe,gBAAgB,YAAY,EAAE;AAElE,wBAAsB,eAAe,gBAAgB,aAAa,EAAE;AAIpE,oBAAkB,UAAU;AAE5B,oBAAkB,cAAc;AAEhC,oBAAkB,eAAe;AAEjC,oBAAkB,WAAW;AAE7B,oBAAkB,MAAM;AAGxB,OAAK,UAAU,MAAM;AAErB,MAAI,cAAK;AAER,sBAAkB,gBAAgB;AAElC,kCAA8B;AAAA,EAC/B;AACD;AAMO,SAAS,YAAY,QAAQ,IAAI;AACvC,SAAO,SAAS,eAAe,KAAK;AACrC;AAQO,SAAS,gBAAgB,MAAM;AACrC,SAAO,mBAAmB,KAAK,IAAI;AACpC;AAQO,SAAS,iBAAiB,MAAM;AACtC,SAAO,oBAAoB,KAAK,IAAI;AACrC;AASO,SAAS,MAAM,MAAM,SAAS;AACpC,MAAI,CAAC,WAAW;AACf,WAAO,gBAAgB,IAAI;AAAA,EAC5B;AAEA,MAAIC;AAAA;AAAA,IAAqC,gBAAgB,YAAY;AAAA;AAGrE,MAAIA,WAAU,MAAM;AACnB,IAAAA,SAAQ,aAAa,YAAY,YAAY,CAAC;AAAA,EAC/C,WAAW,WAAWA,OAAM,aAAa,GAAG;AAC3C,QAAI,OAAO,YAAY;AACvB,IAAAA,UAAA,gBAAAA,OAAO,OAAO;AACd,qBAAiB,IAAI;AACrB,WAAO;AAAA,EACR;AAEA,mBAAiBA,MAAK;AACtB,SAAOA;AACR;AAQO,SAAS,YAAY,UAAU,SAAS;AA3H/C;AA4HC,MAAI,CAAC,WAAW;AAEf,QAAI;AAAA;AAAA,MAAyC;AAAA;AAAA,QAAqC;AAAA,MAAS;AAAA;AAG3F,QAAI,iBAAiB,WAAW,MAAM,SAAS,GAAI,QAAO,iBAAiB,KAAK;AAEhF,WAAO;AAAA,EACR;AAIA,MAAI,aAAW,yCAAc,cAAa,GAAG;AAC5C,QAAI,OAAO,YAAY;AAEvB,8CAAc,OAAO;AACrB,qBAAiB,IAAI;AACrB,WAAO;AAAA,EACR;AAEA,SAAO;AACR;AASO,SAAS,QAAQ,MAAM,QAAQ,GAAG,UAAU,OAAO;AACzD,MAAI,eAAe,YAAY,eAAe;AAC9C,MAAI;AAEJ,SAAO,SAAS;AACf,mBAAe;AACf;AAAA,IAA4C,iBAAiB,YAAY;AAAA,EAC1E;AAEA,MAAI,CAAC,WAAW;AACf,WAAO;AAAA,EACR;AAEA,MAAI,OAAO,6CAAc;AAIzB,MAAI,WAAW,SAAS,GAAG;AAC1B,QAAI,OAAO,YAAY;AAIvB,QAAI,iBAAiB,MAAM;AAC1B,mDAAc,MAAM;AAAA,IACrB,OAAO;AACN,mBAAa,OAAO,IAAI;AAAA,IACzB;AACA,qBAAiB,IAAI;AACrB,WAAO;AAAA,EACR;AAEA,mBAAiB,YAAY;AAC7B;AAAA;AAAA,IAAoC;AAAA;AACrC;AAOO,SAAS,mBAAmB,MAAM;AACxC,OAAK,cAAc;AACpB;;;AClJO,SAAS,gBAAgB,MAAM;AACrC,MAAI,kBAAkB,QAAQ,oBAAoB,MAAM;AACvD,IAAE,cAAc,IAAI;AAAA,EACrB;AAEA,MAAI,oBAAoB,SAAS,gBAAgB,IAAI,aAAa,GAAG;AACpE,IAAE,0BAA0B;AAAA,EAC7B;AAEA,MAAI,sBAAsB;AACzB,IAAE,mBAAmB,IAAI;AAAA,EAC1B;AACD;AAMA,SAAS,YAAYC,SAAQ,eAAe;AAC3C,MAAI,cAAc,cAAc;AAChC,MAAI,gBAAgB,MAAM;AACzB,kBAAc,OAAO,cAAc,QAAQA;AAAA,EAC5C,OAAO;AACN,gBAAY,OAAOA;AACnB,IAAAA,QAAO,OAAO;AACd,kBAAc,OAAOA;AAAA,EACtB;AACD;AASA,SAAS,cAAc,MAAM,IAAI,MAAMC,QAAO,MAAM;AACnD,MAAI,WAAW,OAAO,iBAAiB;AACvC,MAAI,gBAAgB;AAEpB,MAAI,cAAK;AAER,WAAO,kBAAkB,SAAS,cAAc,IAAI,oBAAoB,GAAG;AAC1E,sBAAgB,cAAc;AAAA,IAC/B;AAAA,EACD;AAGA,MAAID,UAAS;AAAA,IACZ,KAAK;AAAA,IACL,MAAM;AAAA,IACN,UAAU;AAAA,IACV,aAAa;AAAA,IACb,WAAW;AAAA,IACX,GAAG,OAAO;AAAA,IACV,OAAO;AAAA,IACP;AAAA,IACA,MAAM;AAAA,IACN,MAAM;AAAA,IACN,QAAQ,UAAU,OAAO;AAAA,IACzB,MAAM;AAAA,IACN,UAAU;AAAA,IACV,aAAa;AAAA,IACb,IAAI;AAAA,EACL;AAEA,MAAI,cAAK;AACR,IAAAA,QAAO,qBAAqB;AAAA,EAC7B;AAEA,MAAI,MAAM;AACT,QAAI,6BAA6B;AAEjC,QAAI;AACH,6BAAuB,IAAI;AAC3B,oBAAcA,OAAM;AACpB,MAAAA,QAAO,KAAK;AAAA,IACb,SAAS,GAAG;AACX,qBAAeA,OAAM;AACrB,YAAM;AAAA,IACP,UAAE;AACD,6BAAuB,0BAA0B;AAAA,IAClD;AAAA,EACD,WAAW,OAAO,MAAM;AACvB,oBAAgBA,OAAM;AAAA,EACvB;AAIA,MAAI,QACH,QACAA,QAAO,SAAS,QAChBA,QAAO,UAAU,QACjBA,QAAO,gBAAgB,QACvBA,QAAO,aAAa,SACnBA,QAAO,KAAK,qBAAqB,sBAAsB;AAEzD,MAAI,CAAC,SAAS,CAAC,WAAWC,OAAM;AAC/B,QAAI,kBAAkB,MAAM;AAC3B,kBAAYD,SAAQ,aAAa;AAAA,IAClC;AAGA,QAAI,oBAAoB,SAAS,gBAAgB,IAAI,aAAa,GAAG;AACpE,UAAIE;AAAA;AAAA,QAAkC;AAAA;AACtC,OAACA,SAAQ,aAARA,SAAQ,WAAa,CAAC,IAAG,KAAKF,OAAM;AAAA,IACtC;AAAA,EACD;AAEA,SAAOA;AACR;AAMO,SAAS,kBAAkB;AACjC,MAAI,oBAAoB,MAAM;AAC7B,WAAO;AAAA,EACR;AAIA,SAAO,CAAC;AACT;AAKO,SAAS,SAAS,IAAI;AAC5B,QAAMA,UAAS,cAAc,eAAe,MAAM,KAAK;AACvD,oBAAkBA,SAAQ,KAAK;AAC/B,EAAAA,QAAO,WAAW;AAClB,SAAOA;AACR;AAMO,SAAS,YAAY,IAAI;AAC/B,kBAAgB,SAAS;AAIzB,MAAI,QACH,kBAAkB,SACjB,cAAc,IAAI,mBAAmB,KACtC,sBAAsB,QACtB,CAAC,kBAAkB;AAEpB,MAAI,cAAK;AACR,oBAAgB,IAAI,QAAQ;AAAA,MAC3B,OAAO;AAAA,IACR,CAAC;AAAA,EACF;AAEA,MAAI,OAAO;AACV,QAAI;AAAA;AAAA,MAA2C;AAAA;AAC/C,KAAC,QAAQ,MAAR,QAAQ,IAAM,CAAC,IAAG,KAAK;AAAA,MACvB;AAAA,MACA,QAAQ;AAAA,MACR,UAAU;AAAA,IACX,CAAC;AAAA,EACF,OAAO;AACN,QAAI,SAAS,OAAO,EAAE;AACtB,WAAO;AAAA,EACR;AACD;AAOO,SAAS,gBAAgB,IAAI;AACnC,kBAAgB,aAAa;AAC7B,MAAI,cAAK;AACR,oBAAgB,IAAI,QAAQ;AAAA,MAC3B,OAAO;AAAA,IACR,CAAC;AAAA,EACF;AACA,SAAO,cAAc,EAAE;AACxB;AAGO,SAAS,eAAe,IAAI;AAClC,SAAO,cAAc,gBAAgB,IAAI,IAAI;AAC9C;AAOO,SAAS,YAAY,IAAI;AAC/B,QAAMA,UAAS,cAAc,aAAa,IAAI,IAAI;AAElD,SAAO,MAAM;AACZ,mBAAeA,OAAM;AAAA,EACtB;AACD;AAOO,SAAS,eAAe,IAAI;AAClC,QAAMA,UAAS,cAAc,aAAa,IAAI,IAAI;AAElD,SAAO,CAAC,UAAU,CAAC,MAAM;AACxB,WAAO,IAAI,QAAQ,CAAC,WAAW;AAC9B,UAAI,QAAQ,OAAO;AAClB,qBAAaA,SAAQ,MAAM;AAC1B,yBAAeA,OAAM;AACrB,iBAAO,MAAS;AAAA,QACjB,CAAC;AAAA,MACF,OAAO;AACN,uBAAeA,OAAM;AACrB,eAAO,MAAS;AAAA,MACjB;AAAA,IACD,CAAC;AAAA,EACF;AACD;AAMO,SAAS,OAAO,IAAI;AAC1B,SAAO,cAAc,QAAQ,IAAI,KAAK;AACvC;AAOO,SAAS,kBAAkB,MAAM,IAAI;AAC3C,MAAI;AAAA;AAAA,IAAiD;AAAA;AAGrD,MAAI,QAAQ,EAAE,QAAQ,MAAM,KAAK,MAAM;AACvC,UAAQ,EAAE,GAAG,KAAK,KAAK;AAEvB,QAAM,SAAS,cAAc,MAAM;AAClC,SAAK;AAIL,QAAI,MAAM,IAAK;AAEf,UAAM,MAAM;AACZ,QAAI,QAAQ,EAAE,IAAI,IAAI;AACtB,YAAQ,EAAE;AAAA,EACX,CAAC;AACF;AAEO,SAAS,0BAA0B;AACzC,MAAI;AAAA;AAAA,IAAiD;AAAA;AAErD,gBAAc,MAAM;AACnB,QAAI,CAAC,IAAI,QAAQ,EAAE,EAAE,EAAG;AAGxB,aAAS,SAAS,QAAQ,EAAE,IAAI;AAC/B,UAAIA,UAAS,MAAM;AAInB,WAAKA,QAAO,IAAI,WAAW,GAAG;AAC7B,0BAAkBA,SAAQ,WAAW;AAAA,MACtC;AAEA,UAAI,gBAAgBA,OAAM,GAAG;AAC5B,sBAAcA,OAAM;AAAA,MACrB;AAEA,YAAM,MAAM;AAAA,IACb;AAEA,YAAQ,EAAE,GAAG,IAAI;AAAA,EAClB,CAAC;AACF;AAMO,SAAS,cAAc,IAAI;AACjC,SAAO,cAAc,eAAe,IAAI,IAAI;AAC7C;AAMO,SAAS,gBAAgB,IAAI;AACnC,MAAI,cAAK;AACR,oBAAgB,IAAI,QAAQ;AAAA,MAC3B,OAAO;AAAA,IACR,CAAC;AAAA,EACF;AACA,SAAO,MAAM,EAAE;AAChB;AAMO,SAAS,MAAM,IAAI,QAAQ,GAAG;AACpC,SAAO,cAAc,gBAAgB,eAAe,OAAO,IAAI,IAAI;AACpE;AAMO,SAAS,OAAO,IAAIC,QAAO,MAAM;AACvC,SAAO,cAAc,gBAAgB,eAAe,IAAI,MAAMA,KAAI;AACnE;AAKO,SAAS,wBAAwBD,SAAQ;AAC/C,MAAIG,YAAWH,QAAO;AACtB,MAAIG,cAAa,MAAM;AACtB,UAAM,+BAA+B;AACrC,UAAM,oBAAoB;AAC1B,6BAAyB,IAAI;AAC7B,wBAAoB,IAAI;AACxB,QAAI;AACH,MAAAA,UAAS,KAAK,IAAI;AAAA,IACnB,UAAE;AACD,+BAAyB,4BAA4B;AACrD,0BAAoB,iBAAiB;AAAA,IACtC;AAAA,EACD;AACD;AAMO,SAAS,wBAAwB,QAAQ;AAC/C,MAAI,WAAW,OAAO;AAEtB,MAAI,aAAa,MAAM;AACtB,WAAO,WAAW;AAElB,aAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK,GAAG;AAC5C,sBAAgB,SAAS,CAAC,CAAC;AAAA,IAC5B;AAAA,EACD;AACD;AAOO,SAAS,wBAAwB,QAAQ,aAAa,OAAO;AACnE,MAAIH,UAAS,OAAO;AACpB,SAAO,QAAQ,OAAO,OAAO;AAE7B,SAAOA,YAAW,MAAM;AACvB,QAAII,QAAOJ,QAAO;AAClB,mBAAeA,SAAQ,UAAU;AACjC,IAAAA,UAASI;AAAA,EACV;AACD;AAMO,SAAS,8BAA8B,QAAQ;AACrD,MAAIJ,UAAS,OAAO;AAEpB,SAAOA,YAAW,MAAM;AACvB,QAAII,QAAOJ,QAAO;AAClB,SAAKA,QAAO,IAAI,mBAAmB,GAAG;AACrC,qBAAeA,OAAM;AAAA,IACtB;AACA,IAAAA,UAASI;AAAA,EACV;AACD;AAOO,SAAS,eAAeJ,SAAQ,aAAa,MAAM;AACzD,MAAI,UAAU;AAEd,OAAK,eAAeA,QAAO,IAAI,iBAAiB,MAAMA,QAAO,gBAAgB,MAAM;AAElF,QAAI,OAAOA,QAAO;AAClB,QAAI,MAAMA,QAAO;AAEjB,WAAO,SAAS,MAAM;AAErB,UAAII,QAAO,SAAS,MAAM;AAAA;AAAA,QAAoC,iBAAiB,IAAI;AAAA;AAEnF,WAAK,OAAO;AACZ,aAAOA;AAAA,IACR;AAEA,cAAU;AAAA,EACX;AAEA,0BAAwBJ,SAAQ,cAAc,CAAC,OAAO;AACtD,0BAAwBA,OAAM;AAC9B,mBAAiBA,SAAQ,CAAC;AAC1B,oBAAkBA,SAAQ,SAAS;AAEnC,MAAI,cAAcA,QAAO;AAEzB,MAAI,gBAAgB,MAAM;AACzB,eAAW,cAAc,aAAa;AACrC,iBAAW,KAAK;AAAA,IACjB;AAAA,EACD;AAEA,0BAAwBA,OAAM;AAE9B,MAAI,SAASA,QAAO;AAGpB,MAAI,WAAW,QAAQ,OAAO,UAAU,MAAM;AAC7C,kBAAcA,OAAM;AAAA,EACrB;AAEA,MAAI,cAAK;AACR,IAAAA,QAAO,qBAAqB;AAAA,EAC7B;AAIA,EAAAA,QAAO,OACNA,QAAO,OACPA,QAAO,WACPA,QAAO,MACPA,QAAO,OACPA,QAAO,KACPA,QAAO,cACPA,QAAO,YACN;AACH;AAOO,SAAS,cAAcA,SAAQ;AACrC,MAAI,SAASA,QAAO;AACpB,MAAI,OAAOA,QAAO;AAClB,MAAII,QAAOJ,QAAO;AAElB,MAAI,SAAS,KAAM,MAAK,OAAOI;AAC/B,MAAIA,UAAS,KAAM,CAAAA,MAAK,OAAO;AAE/B,MAAI,WAAW,MAAM;AACpB,QAAI,OAAO,UAAUJ,QAAQ,QAAO,QAAQI;AAC5C,QAAI,OAAO,SAASJ,QAAQ,QAAO,OAAO;AAAA,EAC3C;AACD;AAWO,SAAS,aAAaA,SAAQ,UAAU;AAE9C,MAAI,cAAc,CAAC;AAEnB,iBAAeA,SAAQ,aAAa,IAAI;AAExC,sBAAoB,aAAa,MAAM;AACtC,mBAAeA,OAAM;AACrB,QAAI,SAAU,UAAS;AAAA,EACxB,CAAC;AACF;AAMO,SAAS,oBAAoB,aAAa,IAAI;AACpD,MAAI,YAAY,YAAY;AAC5B,MAAI,YAAY,GAAG;AAClB,QAAI,QAAQ,MAAM,EAAE,aAAa,GAAG;AACpC,aAAS,cAAc,aAAa;AACnC,iBAAW,IAAI,KAAK;AAAA,IACrB;AAAA,EACD,OAAO;AACN,OAAG;AAAA,EACJ;AACD;AAOO,SAAS,eAAeA,SAAQ,aAAa,OAAO;AAC1D,OAAKA,QAAO,IAAI,WAAW,EAAG;AAC9B,EAAAA,QAAO,KAAK;AAEZ,MAAIA,QAAO,gBAAgB,MAAM;AAChC,eAAW,cAAcA,QAAO,aAAa;AAC5C,UAAI,WAAW,aAAa,OAAO;AAClC,oBAAY,KAAK,UAAU;AAAA,MAC5B;AAAA,IACD;AAAA,EACD;AAEA,MAAIK,SAAQL,QAAO;AAEnB,SAAOK,WAAU,MAAM;AACtB,QAAIC,WAAUD,OAAM;AACpB,QAAI,eAAeA,OAAM,IAAI,wBAAwB,MAAMA,OAAM,IAAI,mBAAmB;AAIxF,mBAAeA,QAAO,aAAa,cAAc,QAAQ,KAAK;AAC9D,IAAAA,SAAQC;AAAA,EACT;AACD;AAOO,SAAS,cAAcN,SAAQ;AACrC,kBAAgBA,SAAQ,IAAI;AAC7B;AAMA,SAAS,gBAAgBA,SAAQ,OAAO;AACvC,OAAKA,QAAO,IAAI,WAAW,EAAG;AAC9B,EAAAA,QAAO,KAAK;AAIZ,OAAKA,QAAO,IAAI,WAAW,GAAG;AAC7B,IAAAA,QAAO,KAAK;AAAA,EACb;AAIA,MAAI,gBAAgBA,OAAM,GAAG;AAC5B,sBAAkBA,SAAQ,KAAK;AAC/B,oBAAgBA,OAAM;AAAA,EACvB;AAEA,MAAIK,SAAQL,QAAO;AAEnB,SAAOK,WAAU,MAAM;AACtB,QAAIC,WAAUD,OAAM;AACpB,QAAI,eAAeA,OAAM,IAAI,wBAAwB,MAAMA,OAAM,IAAI,mBAAmB;AAIxF,oBAAgBA,QAAO,cAAc,QAAQ,KAAK;AAClD,IAAAA,SAAQC;AAAA,EACT;AAEA,MAAIN,QAAO,gBAAgB,MAAM;AAChC,eAAW,cAAcA,QAAO,aAAa;AAC5C,UAAI,WAAW,aAAa,OAAO;AAClC,mBAAW,GAAG;AAAA,MACf;AAAA,IACD;AAAA,EACD;AACD;;;ACrnBO,IAAI,sBAAsB;AAMjC,SAAS,UAAU,QAAQ,OAAO;AACjC,QAAM,QAAQ,OAAO;AACrB,QAAM,QAAQ,OAAO,sBAAsB,OAAO,UAAU,OAAO;AAEnE,MAAI,UAAU,eAAe;AAC5B;AAAA,EACD;AAEA,MAAI,OAAO;AACV,QAAI,4BAA4B;AAChC,QAAI,WAAW,oBAAI,IAAI;AACvB,yBAAqB,QAAQ;AAC7B,QAAI;AACH,cAAQ,MAAM;AACb,cAAM;AAAA,MACP,CAAC;AAAA,IACF,UAAE;AACD,2BAAqB,yBAAyB;AAAA,IAC/C;AACA,QAAI,SAAS,OAAO,GAAG;AACtB,iBAAW,OAAO,UAAU;AAC3B,kBAAU,GAAG;AAAA,MACd;AACA;AAAA,IACD;AAAA,EACD;AAEA,QAAM,QAAQ,OAAO,IAAI,aAAa,IAAI,aAAa;AACvD,QAAM;AAAA;AAAA,IAA4C;AAAA;AAClD,QAAM,QAAQ,OAAO,KAAK,iBAAiB,MAAM,iBAAiB,OAAO;AAGzE,UAAQ;AAAA,IACP,KAAK,IAAI;AAAA,IACT,QAAQ,6CAA6C;AAAA,IACrD,OAAO,UAAU,YAAY,UAAU,QAAQ,gBAAgB,QAC5D,SAAS,OAAO,IAAI,IACpB;AAAA,EACJ;AAEA,MAAI,SAAS,YAAY;AACxB,UAAM,OAAO,IAAI;AAAA;AAAA,MAA4B,OAAQ;AAAA,IAAI;AACzD,eAAW,OAAO,MAAM;AACvB,gBAAU,GAAG;AAAA,IACd;AAAA,EACD;AAEA,MAAI,OAAO,SAAS;AAEnB,YAAQ,IAAI,OAAO,OAAO;AAAA,EAC3B;AAEA,MAAI,OAAO,SAAS;AAEnB,YAAQ,IAAI,OAAO,OAAO;AAAA,EAC3B;AAEA,QAAM,OAAO,+BAAO;AAEpB,MAAI,QAAQ,KAAK,SAAS,GAAG;AAC5B,aAASO,UAAS,MAAM;AAEvB,cAAQ,IAAIA,MAAK;AAAA,IAClB;AAAA,EACD;AAGA,UAAQ,SAAS;AAClB;AAOO,SAAS,MAAM,OAAO,IAAI;AAChC,MAAI,iCAAiC;AACrC,MAAI;AACH,0BAAsB,EAAE,SAAS,oBAAI,IAAI,GAAG,UAAU,gBAAgB;AAEtE,QAAI,QAAQ,YAAY,IAAI;AAC5B,QAAI,QAAQ,GAAG;AACf,QAAI,QAAQ,YAAY,IAAI,IAAI,OAAO,QAAQ,CAAC;AAEhD,QAAI,CAAC,gBAAgB,GAAG;AAEvB,cAAQ,IAAI,GAAG,MAAM,CAAC,gCAAgC,IAAI,OAAO,aAAa;AAAA,IAC/E,WAAW,oBAAoB,QAAQ,SAAS,GAAG;AAElD,cAAQ,IAAI,GAAG,MAAM,CAAC,gCAAgC,IAAI,OAAO,aAAa;AAAA,IAC/E,OAAO;AAEN,cAAQ,MAAM,GAAG,MAAM,CAAC,OAAO,IAAI,OAAO,aAAa;AAEvD,UAAI,UAAU,oBAAoB;AAElC,4BAAsB;AAEtB,iBAAW,CAAC,QAAQ,KAAK,KAAK,SAAS;AACtC,kBAAU,QAAQ,KAAK;AAAA,MACxB;AAEA,cAAQ,SAAS;AAAA,IAClB;AAEA,QAAI,mCAAmC,QAAQ,wBAAwB,MAAM;AAC5E,iBAAW,CAAC,QAAQ,KAAK,KAAK,oBAAoB,SAAS;AAC1D,YAAI,aAAa,+BAA+B,IAAI,MAAM;AAE1D,YAAI,eAAe,QAAW;AAC7B,yCAA+B,IAAI,QAAQ,KAAK;AAAA,QACjD,OAAO;AACN,qBAAW,KAAK,KAAK,GAAG,MAAM,IAAI;AAAA,QACnC;AAAA,MACD;AAAA,IACD;AAEA,WAAO;AAAA,EACR,UAAE;AACD,0BAAsB;AAAA,EACvB;AACD;AAKO,SAASC,WAAU,OAAO;AAChC,MAAI,QAAQ,MAAM;AAClB,QAAMD,SAAQ,MAAM;AAEpB,MAAIA,QAAO;AACV,UAAM,QAAQA,OAAM,MAAM,IAAI;AAC9B,UAAM,YAAY,CAAC,IAAI;AAEvB,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACtC,YAAM,OAAO,MAAM,CAAC;AAEpB,UAAI,SAAS,SAAS;AACrB;AAAA,MACD;AACA,UAAI,KAAK,SAAS,oBAAoB,GAAG;AACxC,eAAO;AAAA,MACR;AACA,UAAI,KAAK,SAAS,qBAAqB,GAAG;AACzC;AAAA,MACD;AACA,gBAAU,KAAK,IAAI;AAAA,IACpB;AAEA,QAAI,UAAU,WAAW,GAAG;AAC3B,aAAO;AAAA,IACR;AAEA,oBAAgB,OAAO,SAAS;AAAA,MAC/B,OAAO,UAAU,KAAK,IAAI;AAAA,IAC3B,CAAC;AAED,oBAAgB,OAAO,QAAQ;AAAA;AAAA,MAE9B,OAAO,GAAG,KAAK;AAAA,IAChB,CAAC;AAAA,EACF;AACA,SAAO;AACR;;;ACzKO,SAAS,UAAU,KAAK,OAAO;AACrC,MAAI,OAAO;AACV,UAAM,OAAO,SAAS;AACtB,QAAI,YAAY;AAEhB,qBAAiB,MAAM;AACtB,UAAI,SAAS,kBAAkB,MAAM;AACpC,YAAI,MAAM;AAAA,MACX;AAAA,IACD,CAAC;AAAA,EACF;AACD;AAQO,SAAS,sBAAsB,KAAK;AAC1C,MAAI,aAAa,gBAAgB,GAAG,MAAM,MAAM;AAC/C,uBAAmB,GAAG;AAAA,EACvB;AACD;AAEA,IAAI,0BAA0B;AAEvB,SAAS,0BAA0B;AACzC,MAAI,CAAC,yBAAyB;AAC7B,8BAA0B;AAC1B,aAAS;AAAA,MACR;AAAA,MACA,CAAC,QAAQ;AAGR,gBAAQ,QAAQ,EAAE,KAAK,MAAM;AA5CjC;AA6CK,cAAI,CAAC,IAAI,kBAAkB;AAC1B;AAAA,oBAAW;AAAA;AAAA,cAAoC,IAAI,OAAQ;AAAA,cAAU;AAEpE,sBAAE,WAAF;AAAA,YACD;AAAA,UACD;AAAA,QACD,CAAC;AAAA,MACF;AAAA;AAAA,MAEA,EAAE,SAAS,KAAK;AAAA,IACjB;AAAA,EACD;AACD;;;ACxCO,SAAS,OAAO,QAAQ,QAAQ,SAAS,2BAA2B,MAAM;AAChF,MAAI,0BAA0B;AAC7B,YAAQ;AAAA,EACT;AAEA,WAAS,QAAQ,QAAQ;AACxB,WAAO,iBAAiB,MAAM,OAAO;AAAA,EACtC;AAEA,WAAS,MAAM;AACd,aAASE,SAAQ,QAAQ;AACxB,aAAO,oBAAoBA,OAAM,OAAO;AAAA,IACzC;AAAA,EACD,CAAC;AACF;AAMO,SAAS,yBAAyB,IAAI;AAC5C,MAAI,oBAAoB;AACxB,MAAI,kBAAkB;AACtB,sBAAoB,IAAI;AACxB,oBAAkB,IAAI;AACtB,MAAI;AACH,WAAO,GAAG;AAAA,EACX,UAAE;AACD,wBAAoB,iBAAiB;AACrC,sBAAkB,eAAe;AAAA,EAClC;AACD;AAUO,SAAS,gCAAgC,SAASC,QAAO,SAAS,WAAW,SAAS;AAC5F,UAAQ,iBAAiBA,QAAO,MAAM,yBAAyB,OAAO,CAAC;AAEvE,QAAM,OAAO,QAAQ;AACrB,MAAI,MAAM;AAGT,YAAQ,SAAS,MAAM;AACtB,WAAK;AACL,eAAS,IAAI;AAAA,IACd;AAAA,EACD,OAAO;AAEN,YAAQ,SAAS,MAAM,SAAS,IAAI;AAAA,EACrC;AAEA,0BAAwB;AACzB;;;AC3DO,IAAM,wBAAwB,oBAAI,IAAI;AAGtC,IAAM,qBAAqB,oBAAI,IAAI;AAOnC,SAAS,cAAc,KAAK;AAClC,MAAI,CAAC,UAAW;AAEhB,MAAI,IAAI,QAAQ;AACf,QAAI,gBAAgB,QAAQ;AAAA,EAC7B;AACA,MAAI,IAAI,SAAS;AAChB,QAAI,gBAAgB,SAAS;AAAA,EAC9B;AAEA,QAAMC,SAAQ,IAAI;AAClB,MAAIA,WAAU,QAAW;AAExB,QAAI,MAAM;AACV,mBAAe,MAAM;AACpB,UAAI,IAAI,aAAa;AACpB,YAAI,cAAcA,MAAK;AAAA,MACxB;AAAA,IACD,CAAC;AAAA,EACF;AACD;AAQO,SAAS,aAAa,YAAY,KAAK,SAAS,SAAS;AAI/D,WAAS,eAAoCA,QAAO;AACnD,QAAI,CAAC,QAAQ,SAAS;AAErB,+BAAyB,KAAK,KAAKA,MAAK;AAAA,IACzC;AACA,QAAI,CAACA,OAAM,cAAc;AACxB,aAAO,yBAAyB,MAAM;AACrC,eAAO,QAAQ,KAAK,MAAMA,MAAK;AAAA,MAChC,CAAC;AAAA,IACF;AAAA,EACD;AAMA,MACC,WAAW,WAAW,SAAS,KAC/B,WAAW,WAAW,OAAO,KAC7B,eAAe,SACd;AACD,qBAAiB,MAAM;AACtB,UAAI,iBAAiB,YAAY,gBAAgB,OAAO;AAAA,IACzD,CAAC;AAAA,EACF,OAAO;AACN,QAAI,iBAAiB,YAAY,gBAAgB,OAAO;AAAA,EACzD;AAEA,SAAO;AACR;AAYO,SAAS,GAAG,SAAS,MAAM,SAAS,UAAU,CAAC,GAAG;AACxD,MAAI,iBAAiB,aAAa,MAAM,SAAS,SAAS,OAAO;AAEjE,SAAO,MAAM;AACZ,YAAQ,oBAAoB,MAAM,gBAAgB,OAAO;AAAA,EAC1D;AACD;AAUO,SAAS,MAAM,YAAY,KAAK,SAAS,SAAS,SAAS;AACjE,MAAI,UAAU,EAAE,SAAS,QAAQ;AACjC,MAAI,iBAAiB,aAAa,YAAY,KAAK,SAAS,OAAO;AAGnE,MAAI,QAAQ,SAAS,QAAQ,QAAQ,UAAU,QAAQ,UAAU;AAChE,aAAS,MAAM;AACd,UAAI,oBAAoB,YAAY,gBAAgB,OAAO;AAAA,IAC5D,CAAC;AAAA,EACF;AACD;AAMO,SAAS,SAAS,QAAQ;AAChC,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACvC,0BAAsB,IAAI,OAAO,CAAC,CAAC;AAAA,EACpC;AAEA,WAAS,MAAM,oBAAoB;AAClC,OAAG,MAAM;AAAA,EACV;AACD;AAOO,SAAS,yBAAyBA,QAAO;AAlJhD;AAmJC,MAAI,kBAAkB;AACtB,MAAI;AAAA;AAAA,IAAsC,gBAAiB;AAAA;AAC3D,MAAI,aAAaA,OAAM;AACvB,MAAI,SAAO,KAAAA,OAAM,iBAAN,wBAAAA,YAA0B,CAAC;AACtC,MAAI;AAAA;AAAA,IAAgD,KAAK,CAAC,KAAKA,OAAM;AAAA;AAMrE,MAAI,WAAW;AAGf,MAAI,aAAaA,OAAM;AAEvB,MAAI,YAAY;AACf,QAAI,SAAS,KAAK,QAAQ,UAAU;AACpC,QACC,WAAW,OACV,oBAAoB,YAAY;AAAA,IAAwC,SACxE;AAKD,MAAAA,OAAM,SAAS;AACf;AAAA,IACD;AAOA,QAAI,cAAc,KAAK,QAAQ,eAAe;AAC9C,QAAI,gBAAgB,IAAI;AAGvB;AAAA,IACD;AAEA,QAAI,UAAU,aAAa;AAC1B,iBAAW;AAAA,IACZ;AAAA,EACD;AAEA;AAAA,EAAyC,KAAK,QAAQ,KAAKA,OAAM;AAIjE,MAAI,mBAAmB,gBAAiB;AAGxC,kBAAgBA,QAAO,iBAAiB;AAAA,IACvC,cAAc;AAAA,IACd,MAAM;AACL,aAAO,kBAAkB;AAAA,IAC1B;AAAA,EACD,CAAC;AAOD,MAAI,oBAAoB;AACxB,MAAI,kBAAkB;AACtB,sBAAoB,IAAI;AACxB,oBAAkB,IAAI;AAEtB,MAAI;AAIH,QAAI;AAIJ,QAAI,eAAe,CAAC;AAEpB,WAAO,mBAAmB,MAAM;AAE/B,UAAI,iBACH,eAAe,gBACf,eAAe;AAAA,MACK,eAAgB,QACpC;AAED,UAAI;AAEH,YAAI,YAAY,eAAe,OAAO,UAAU;AAEhD,YAAI,cAAc,UAAa;AAAA,QAAsB,eAAgB,UAAW;AAC/E,cAAI,SAAS,SAAS,GAAG;AACxB,gBAAI,CAAC,IAAI,GAAG,IAAI,IAAI;AACpB,eAAG,MAAM,gBAAgB,CAACA,QAAO,GAAG,IAAI,CAAC;AAAA,UAC1C,OAAO;AACN,sBAAU,KAAK,gBAAgBA,MAAK;AAAA,UACrC;AAAA,QACD;AAAA,MACD,SAAS,OAAO;AACf,YAAI,aAAa;AAChB,uBAAa,KAAK,KAAK;AAAA,QACxB,OAAO;AACN,wBAAc;AAAA,QACf;AAAA,MACD;AACA,UAAIA,OAAM,gBAAgB,mBAAmB,mBAAmB,mBAAmB,MAAM;AACxF;AAAA,MACD;AACA,uBAAiB;AAAA,IAClB;AAEA,QAAI,aAAa;AAChB,eAAS,SAAS,cAAc;AAE/B,uBAAe,MAAM;AACpB,gBAAM;AAAA,QACP,CAAC;AAAA,MACF;AACA,YAAM;AAAA,IACP;AAAA,EACD,UAAE;AAED,IAAAA,OAAM,SAAS;AAEf,WAAOA,OAAM;AACb,wBAAoB,iBAAiB;AACrC,sBAAkB,eAAe;AAAA,EAClC;AACD;AAYO,SAAS,MACf,OACA,SACA,MACA,WACA,KACA,mBAAmB,OACnB,gBAAgB,OACf;AACD,MAAI;AACJ,MAAI;AAEJ,MAAI;AACH,cAAU,MAAM;AAAA,EACjB,SAAS,GAAG;AACX,YAAQ;AAAA,EACT;AAEA,MAAI,OAAO,YAAY,YAAY;AAClC,YAAQ,MAAM,SAAS,IAAI;AAAA,EAC5B,WAAW,oBAAoB,WAAW,QAAQ,OAAO;AACxD,UAAM,WAAW,uCAAY;AAC7B,UAAM,WAAW,MAAM,OAAO,QAAQ,IAAI,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,KAAK,OAAO,QAAQ;AAE9E,UAAM,aAAa,KAAK,CAAC,EAAE;AAC3B,UAAM,cAAc,KAAK,UAAU,aAAa,QAAQ;AACxD,UAAM,aAAa,gBAAgB,6BAA6B;AAEhE,IAAE,sBAAsB,aAAa,UAAU;AAE/C,QAAI,OAAO;AACV,YAAM;AAAA,IACP;AAAA,EACD;AACD;", "names": ["fallback", "stack", "stack", "get_stack", "source", "effect", "get_stack", "derived", "child", "is_micro_task_queued", "effect", "component_context", "stack", "teardown", "sibling", "child", "derived", "get_stack", "get", "next", "stack", "get_stack", "_a", "source", "value", "key", "array_prototype", "equals", "child", "effect", "push", "derived", "teardown", "next", "child", "sibling", "stack", "get_stack", "name", "event", "event"]}
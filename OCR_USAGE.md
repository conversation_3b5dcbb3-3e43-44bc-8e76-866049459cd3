# TrOCR OCR 功能使用指南

本项目现已集成基于 Hugging Face TrOCR 的 OCR（光学字符识别）功能，支持从图像和 PDF 文件中提取文本。

## 功能特性

- **基于 TrOCR**: 使用 Microsoft 的 TrOCR 模型，提供高质量的文本识别
- **多种模型支持**: 支持手写文本和印刷文本的不同模型
- **多格式支持**: 支持 JPG、PNG、PDF 等格式
- **API 集成**: 完全集成到现有的 FastAPI 后端
- **前端支持**: 前端界面支持 OCR 文件的上传和处理

## 支持的文件格式

- **图像文件**: JPG, JPEG, PNG, GIF, BMP, TIFF, TIF, WEBP
- **PDF 文件**: 包括扫描版 PDF（自动检测并使用 OCR）
- **文本文件**: TXT, TEX, JSON（原有功能保持不变）

## 可用的 TrOCR 模型

1. **microsoft/trocr-base-handwritten** (默认)
   - 适用于手写文本识别
   - 模型较小，速度较快

2. **microsoft/trocr-base-printed**
   - 适用于印刷文本识别
   - 对打印文档效果更好

3. **microsoft/trocr-large-handwritten**
   - 大型手写文本模型
   - 准确率更高，但速度较慢

4. **microsoft/trocr-large-printed**
   - 大型印刷文本模型
   - 最高准确率，适合高质量要求

## API 使用方法

### 1. 上传并解析文件

```bash
# 上传二进制文件（图像或PDF）
curl -X POST "http://127.0.0.1:8000/parse/upload/binary" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@your_image.jpg"

# 解析上传的文件
curl -X POST "http://127.0.0.1:8000/parse/parse/file" \
  -H "Content-Type: application/json" \
  -d '{"file_id": "your_file_id"}'
```

### 2. 直接 OCR 识别

```bash
# 对特定文件进行 OCR
curl -X POST "http://127.0.0.1:8000/parse/parse/ocr/" \
  -H "Content-Type: application/json" \
  -d '{"file_id": "your_file_id"}'

# 对本地文件进行 OCR
curl -X POST "http://127.0.0.1:8000/parse/ocr" \
  -H "Content-Type: application/json" \
  -d '{
    "file_path": "/path/to/your/image.jpg",
    "save_path": "/path/to/save/result"
  }'
```

## 编程接口使用

### Python 代码示例

```python
from text_parse.trocr_ocr import single_ocr_trocr, TrOCREngine

# 方法1: 使用简单函数
result = single_ocr_trocr("path/to/image.jpg")
print(f"识别结果: {result}")

# 方法2: 使用 TrOCR 引擎类
engine = TrOCREngine(model_name="microsoft/trocr-base-printed")
result = engine.recognize_text("path/to/image.jpg")
print(f"识别结果: {result}")

# 方法3: 使用不同输入格式
from PIL import Image

# 文件路径
result1 = single_ocr_trocr("image.jpg")

# PIL Image 对象
image = Image.open("image.jpg")
result2 = single_ocr_trocr(image)

# 字节数据
with open("image.jpg", "rb") as f:
    image_bytes = f.read()
result3 = single_ocr_trocr(image_bytes)
```

## 前端使用

1. **文件上传**: 在文件管理页面，现在可以上传 PDF、JPG、PNG 等格式的文件
2. **自动处理**: 上传后点击"解析"按钮，系统会自动检测文件类型并使用相应的处理方法
3. **进度监控**: 可以实时查看 OCR 处理进度
4. **结果预览**: 处理完成后可以预览识别出的文本内容

## 配置选项

### 模型选择

可以通过修改 `text_parse/trocr_ocr.py` 中的默认模型来改变 OCR 行为：

```python
# 修改默认模型
def single_ocr(image_input):
    return single_ocr_trocr(image_input, model_name="microsoft/trocr-base-printed")
```

### 设备配置

TrOCR 会自动检测可用的计算设备：
- 如果有 CUDA GPU，会优先使用 GPU 加速
- 否则使用 CPU 进行推理

## 性能优化建议

1. **GPU 加速**: 如果有 NVIDIA GPU，安装 CUDA 版本的 PyTorch 可显著提升速度
2. **模型选择**: 根据需求选择合适的模型大小
3. **批处理**: 对于大量图像，可以使用批处理功能
4. **图像预处理**: 确保图像清晰度和对比度良好

## 故障排除

### 常见问题

1. **内存不足**
   - 使用较小的模型（base 而非 large）
   - 减少并行处理数量

2. **识别准确率低**
   - 检查图像质量和清晰度
   - 尝试不同的模型（手写 vs 印刷）
   - 确保文本语言与模型匹配

3. **处理速度慢**
   - 使用 GPU 加速
   - 选择较小的模型
   - 优化图像尺寸

### 日志查看

OCR 处理过程会记录详细日志，可以通过以下方式查看：

```bash
# 查看应用日志
tail -f app.log

# 查看错误日志
curl "http://127.0.0.1:8000/error-logs"
```

## 测试

运行测试脚本验证 OCR 功能：

```bash
python test_trocr_ocr.py
```

该脚本会测试：
- 基本 OCR 功能
- 不同模型的性能
- 各种输入格式的支持
- TrOCR 引擎类的功能

## 更新日志

- **v1.0**: 集成 TrOCR OCR 功能
- 支持多种图像格式和 PDF 文件
- 提供完整的 API 接口
- 前端界面支持 OCR 文件处理
- 自动模型管理和设备检测

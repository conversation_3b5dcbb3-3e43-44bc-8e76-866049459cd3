"""
TrOCR-based OCR implementation for text recognition from images.
This module provides OCR functionality using Hugging Face's TrOCR model.
"""

import os
import logging
from typing import Union, Optional
from PIL import Image
import torch
from transformers import TrOCRProcessor, VisionEncoderDecoderModel
import io

# Set up logging
logger = logging.getLogger(__name__)

class TrOCREngine:
    """
    TrOCR-based OCR engine for text recognition from images.
    """
    
    def __init__(self, model_name: str = "microsoft/trocr-base-handwritten", device: Optional[str] = None):
        """
        Initialize the TrOCR engine.
        
        Args:
            model_name: The TrOCR model to use. Options include:
                       - "microsoft/trocr-base-handwritten" (for handwritten text)
                       - "microsoft/trocr-base-printed" (for printed text)
                       - "microsoft/trocr-large-handwritten" (larger model for handwritten text)
                       - "microsoft/trocr-large-printed" (larger model for printed text)
            device: Device to run the model on ('cuda', 'cpu', or None for auto-detection)
        """
        self.model_name = model_name
        
        # Auto-detect device if not specified
        if device is None:
            self.device = "cuda" if torch.cuda.is_available() else "cpu"
        else:
            self.device = device
            
        logger.info(f"Initializing TrOCR engine with model: {model_name} on device: {self.device}")
        
        # Initialize processor and model
        self.processor = None
        self.model = None
        self._load_model()
    
    def _load_model(self):
        """Load the TrOCR processor and model."""
        try:
            logger.info(f"Loading TrOCR processor from {self.model_name}")
            self.processor = TrOCRProcessor.from_pretrained(self.model_name)
            
            logger.info(f"Loading TrOCR model from {self.model_name}")
            self.model = VisionEncoderDecoderModel.from_pretrained(self.model_name)
            self.model.to(self.device)
            self.model.eval()
            
            logger.info("TrOCR model loaded successfully")
            
        except Exception as e:
            logger.error(f"Failed to load TrOCR model: {str(e)}")
            raise RuntimeError(f"Failed to initialize TrOCR engine: {str(e)}")
    
    def recognize_text(self, image: Union[str, Image.Image, bytes]) -> str:
        """
        Recognize text from an image using TrOCR.
        
        Args:
            image: Input image. Can be:
                  - str: Path to image file
                  - PIL.Image.Image: PIL Image object
                  - bytes: Image data as bytes
        
        Returns:
            str: Recognized text from the image
        """
        try:
            # Convert input to PIL Image
            pil_image = self._prepare_image(image)
            
            # Process image and generate text
            pixel_values = self.processor(pil_image, return_tensors="pt").pixel_values
            pixel_values = pixel_values.to(self.device)
            
            # Generate text using the model
            with torch.no_grad():
                generated_ids = self.model.generate(pixel_values)
            
            # Decode the generated text
            generated_text = self.processor.batch_decode(generated_ids, skip_special_tokens=True)[0]
            
            logger.debug(f"OCR result: {generated_text}")
            return generated_text
            
        except Exception as e:
            logger.error(f"Error during OCR recognition: {str(e)}")
            return ""
    
    def _prepare_image(self, image: Union[str, Image.Image, bytes]) -> Image.Image:
        """
        Convert various image input formats to PIL Image.
        
        Args:
            image: Input image in various formats
            
        Returns:
            PIL.Image.Image: Converted PIL Image
        """
        if isinstance(image, str):
            # File path
            if not os.path.exists(image):
                raise FileNotFoundError(f"Image file not found: {image}")
            return Image.open(image).convert("RGB")
            
        elif isinstance(image, Image.Image):
            # PIL Image
            return image.convert("RGB")
            
        elif isinstance(image, bytes):
            # Bytes data
            return Image.open(io.BytesIO(image)).convert("RGB")
            
        else:
            raise ValueError(f"Unsupported image type: {type(image)}")


# Global TrOCR engine instance
_trocr_engine = None

def get_trocr_engine(model_name: str = "microsoft/trocr-base-handwritten") -> TrOCREngine:
    """
    Get or create a global TrOCR engine instance.
    
    Args:
        model_name: The TrOCR model to use
        
    Returns:
        TrOCREngine: The TrOCR engine instance
    """
    global _trocr_engine
    
    if _trocr_engine is None or _trocr_engine.model_name != model_name:
        logger.info(f"Creating new TrOCR engine with model: {model_name}")
        _trocr_engine = TrOCREngine(model_name)
    
    return _trocr_engine


def single_ocr_trocr(image: Union[str, Image.Image, bytes], 
                     model_name: str = "microsoft/trocr-base-handwritten") -> str:
    """
    Perform OCR on a single image using TrOCR.
    
    This function provides a simple interface for OCR recognition,
    compatible with the existing single_ocr function signature.
    
    Args:
        image: Input image (file path, PIL Image, or bytes)
        model_name: TrOCR model to use for recognition
        
    Returns:
        str: Recognized text from the image
    """
    try:
        engine = get_trocr_engine(model_name)
        result = engine.recognize_text(image)
        return str(result) if result is not None else ""
        
    except Exception as e:
        logger.error(f"OCR recognition failed: {str(e)}")
        return ""


def batch_ocr_trocr(images: list, 
                    model_name: str = "microsoft/trocr-base-handwritten") -> list:
    """
    Perform OCR on multiple images using TrOCR.
    
    Args:
        images: List of images (file paths, PIL Images, or bytes)
        model_name: TrOCR model to use for recognition
        
    Returns:
        list: List of recognized text strings
    """
    try:
        engine = get_trocr_engine(model_name)
        results = []
        
        for image in images:
            try:
                result = engine.recognize_text(image)
                results.append(str(result) if result is not None else "")
            except Exception as e:
                logger.error(f"Failed to process image: {str(e)}")
                results.append("")
        
        return results
        
    except Exception as e:
        logger.error(f"Batch OCR failed: {str(e)}")
        return [""] * len(images)


# Compatibility function to replace the existing single_ocr
def single_ocr(image_input: Union[str, Image.Image, bytes]) -> str:
    """
    OCR function compatible with existing codebase.
    Uses TrOCR for text recognition.
    
    Args:
        image_input: Input image (file path, PIL Image, or bytes)
        
    Returns:
        str: Recognized text from the image
    """
    return single_ocr_trocr(image_input)

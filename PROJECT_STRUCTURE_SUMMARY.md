# LLM-Kit 项目结构总结

## 🏗️ 整体架构

LLM-Kit 是一个**全栈AI文档处理平台**，采用现代化的微服务架构：

- **前端**: Svelte + TypeScript + Tailwind CSS
- **后端**: FastAPI + Python + MongoDB
- **核心功能**: 文档解析、OCR识别、QA生成、质量控制、去重处理

## 📁 目录结构概览

```
LLM-Kit/
├── 🎨 frontend/                    # 前端应用 (Svelte)
│   ├── src/routes/                 # 页面路由
│   ├── src/components/             # 可复用组件
│   └── src/class/                  # TypeScript类型定义
│
├── 🚀 app/                         # 后端核心应用
│   ├── components/core/            # 核心配置和数据库
│   ├── components/models/          # 数据模型定义
│   ├── components/routers/         # API路由层
│   └── components/services/        # 业务逻辑层
│
├── 📄 text_parse/                  # 文档解析模块
│   ├── parse.py                    # 主解析逻辑
│   ├── trocr_ocr.py               # OCR功能 (TrOCR)
│   └── to_tex.py                   # LaTeX转换
│
├── 🤖 model_api/                   # 大模型API接口
│   ├── Qwen/                       # 通义千问
│   ├── erine/                      # 文心一言
│   └── prompts.py                  # 提示词管理
│
├── 🔧 utils/                       # 工具函数
├── ⚙️ hparams/                     # 配置文件
└── 📊 parsed_files/                # 处理结果存储
```

## 🔄 核心工作流程

### 1. 文件处理流程
```
上传文件 → 类型检测 → 解析处理 → 内容存储 → 后续处理
```

### 2. 支持的文件类型
- **文本**: TXT, TEX, JSON
- **文档**: PDF (文本+扫描)
- **图像**: JPG, PNG, GIF, BMP, TIFF, WEBP

### 3. 处理能力
- **OCR识别**: 基于TrOCR的高精度文本识别
- **LaTeX转换**: 文本到LaTeX格式转换
- **QA生成**: 基于内容自动生成问答对
- **质量控制**: 智能质量评估和优化
- **去重处理**: 高效的重复内容检测

## 🎯 主要功能模块

### 前端页面
| 页面 | 功能 | 路径 |
|------|------|------|
| 数据管理 | 文件上传、解析、预览 | `/data` |
| 数据集构建 | QA生成、LaTeX转换 | `/construct` |
| 质量评估 | QA质量控制 | `/quality_eval` |
| 去重管理 | 重复内容处理 | `/deduplication` |
| 记录查看 | 处理历史记录 | `/record` |

### 后端API
| 模块 | 端点前缀 | 主要功能 |
|------|----------|----------|
| 解析 | `/parse` | 文件上传、解析、OCR |
| 转换 | `/to_tex` | LaTeX转换 |
| QA生成 | `/qa` | 问答对生成 |
| 质量控制 | `/quality` | 质量评估 |
| 去重 | `/dedup` | 重复内容处理 |

## 🛠️ 技术特性

### 异步处理
- 文件上传和解析的异步处理
- 实时进度更新
- 后台任务管理

### 多模型支持
- 集成多种大语言模型
- 统一的API接口
- 灵活的模型切换

### 质量保证
- 多层次质量控制
- 自动化质量评估
- 智能优化建议

### 高性能
- MongoDB数据库优化
- 并行处理能力
- 缓存策略

## 🚀 快速开始

### 后端启动
```bash
# 安装依赖
pip install -r requirements.txt

# 启动服务
python main.py
# 或
uvicorn main:app --reload
```

### 前端启动
```bash
cd frontend
npm install
npm run dev
```

### 访问应用
- 前端: http://localhost:5173
- 后端API: http://localhost:8000
- API文档: http://localhost:8000/docs

## 📊 数据库设计

### 主要集合
- `uploaded_files`: 文本文件存储
- `uploaded_binary_files`: 二进制文件存储
- `parse_records`: 解析记录
- `qa_generations`: QA生成记录
- `quality_records`: 质量控制记录
- `dedup_records`: 去重处理记录

## 🔧 配置说明

### 环境变量
```bash
MONGODB_URL=mongodb://localhost:27017
DATABASE_NAME=llm_kit
```

### 模型配置
```yaml
# hparams/config.yaml
model:
  default_model: "qwen-turbo"
  max_tokens: 2048
  temperature: 0.7

ocr:
  default_model: "microsoft/trocr-base-handwritten"
  supported_formats: ["jpg", "png", "pdf", "gif", "bmp", "tiff", "webp"]
```

## 🎨 前端技术栈

- **框架**: SvelteKit
- **语言**: TypeScript
- **样式**: Tailwind CSS
- **组件**: Flowbite Svelte
- **构建**: Vite
- **状态管理**: Svelte Stores

## 🔧 后端技术栈

- **框架**: FastAPI
- **语言**: Python 3.10+
- **数据库**: MongoDB
- **OCR**: TrOCR (Hugging Face)
- **异步**: asyncio
- **验证**: Pydantic

## 📈 性能优化

### 后端优化
- 异步数据库操作
- 并行文件处理
- 智能缓存策略
- 连接池管理

### 前端优化
- 组件懒加载
- 虚拟滚动
- 状态管理优化
- 代码分割

## 🔒 安全考虑

- 文件类型验证
- 输入数据验证
- 错误处理机制
- 日志记录系统

## 📝 开发规范

### 代码结构
- 模块化设计
- 清晰的分层架构
- 统一的错误处理
- 完善的类型定义

### API设计
- RESTful规范
- 统一响应格式
- 详细的错误信息
- 完整的文档

这个项目结构设计确保了系统的**可维护性**、**可扩展性**和**高性能**，为大语言模型相关的文档处理和数据集构建提供了完整的解决方案。

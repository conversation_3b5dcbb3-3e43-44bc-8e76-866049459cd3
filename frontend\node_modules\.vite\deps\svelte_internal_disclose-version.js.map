{"version": 3, "sources": ["../../svelte/src/version.js", "../../svelte/src/internal/disclose-version.js"], "sourcesContent": ["// generated during release, do not modify\n\n/**\n * The current version, as set in package.json.\n * @type {string}\n */\nexport const VERSION = '5.18.0';\nexport const PUBLIC_VERSION = '5';\n", "import { PUBLIC_VERSION } from '../version.js';\n\nif (typeof window !== 'undefined')\n\t// @ts-ignore\n\t(window.__svelte ||= { v: new Set() }).v.add(PUBLIC_VERSION);\n"], "mappings": ";;;AAOO,IAAM,iBAAiB;;;ACL9B,IAAI,OAAO,WAAW;AAErB,GAAC,OAAO,aAAP,OAAO,WAAa,EAAE,GAAG,oBAAI,IAAI,EAAE,IAAG,EAAE,IAAI,cAAc;", "names": []}